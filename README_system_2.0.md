# 差分履带车导航系统 2.0 - 重构版

## 系统概述

这是一个基于ROS2 Humble的完整差分履带车导航系统，采用模块化设计，将轨迹规划和车辆控制功能分离，提高了系统的可维护性和灵活性。

## 系统架构

```
point.txt → global_traj_generate → differential_tracked_navigator → cmd_vel
                                            ↑
                              state_estimation (里程计数据)
```

### 核心模块

1. **global_traj_generate** (C++)
   - 功能：从point.txt文件读取路径点，生成轨迹规划
   - 职责：轨迹规划、路径点管理、导航目标发布
   - 输入：state_estimation (里程计)
   - 输出：/navi_result (导航目标)

2. **differential_tracked** (Python)
   - 功能：差分履带车PID控制器
   - 职责：接收导航目标，执行车辆运动控制
   - 输入：/navi_result, /state_estimation, /registered_scan
   - 输出：/cmd_vel, /way_point

3. **Point_Publisher** (C++)
   - 功能：路径点发布器，用于外部控制或手动测试
   - 职责：手动路径点发布、键盘控制界面
   - 输入：/state_estimation
   - 输出：/waypoint_nav, /arrive_status

## 数据流说明

1. **轨迹规划流程**：
   - `global_traj_generate`读取`point.txt`文件
   - 接收`/state_estimation`获取当前位置
   - 计算当前应该前往的目标点
   - 通过`/navi_result`发布导航目标

2. **车辆控制流程**：
   - `differential_tracked_navigator`接收`/navi_result`
   - 使用PID控制器计算速度命令
   - 通过`/cmd_vel`发布控制指令
   - 同时处理障碍物检测和安全监控

## 文件结构

```
src/
├── differential_tracked/           # 差分履带控制器包
│   ├── differential_tracked_navigator/
│   │   └── differential_tracked_navigator.py
│   ├── config/
│   │   └── navigator_params.yaml
│   ├── launch/
│   │   └── navigation_launch.py
│   ├── maps/
│   │   ├── map.pgm
│   │   └── map.yaml
│   └── rviz/
│       ├── navigation.rviz
│       └── simple.rviz
│
├── global_traj_generate/           # 全局轨迹规划器包
│   ├── src/
│   │   └── global_traj_generate_ros2.cpp
│   ├── msg/
│   │   ├── NavigationResult.msg
│   │   └── NavigationTarget.msg
│   ├── data/
│   │   └── point.txt
│   └── config/
│       └── global_traj_generate_ros2.yaml
│
└── point_publisher/                # 路径点发布器包
    ├── src/
    │   └── point_publish.cpp
    └── config/
        └── point_publish.yaml

launch/
└── diff_tracked_system.launch.py  # 系统启动文件
```

## 使用方法

### 构建系统
```bash
cd /path/to/workspace
colcon build
source install/setup.bash
```

### 启动完整系统
```bash
ros2 launch differential_tracked diff_tracked_system.launch.py
```

### 启动参数
- `config_file`: 导航器配置文件路径
- `waypoints_file`: 路径点文件路径 
- `map_file`: 地图文件路径
- `use_sim_time`: 是否使用仿真时间
- `enable_point_publisher`: 是否启用路径点发布器
- `enable_rviz`: 是否启动RViz可视化

### 示例启动命令
```bash
# 基本启动
ros2 launch differential_tracked diff_tracked_system.launch.py

# 带可视化启动
ros2 launch differential_tracked diff_tracked_system.launch.py enable_rviz:=true

# 仿真模式启动
ros2 launch differential_tracked diff_tracked_system.launch.py use_sim_time:=true enable_rviz:=true
```

## 话题接口

### 输入话题
- `/state_estimation` (nav_msgs/Odometry): 车辆里程计数据
- `/registered_scan` (sensor_msgs/PointCloud2): 激光雷达数据
- `/map` (nav_msgs/OccupancyGrid): 地图数据

### 输出话题
- `/cmd_vel` (geometry_msgs/Twist): 车辆控制指令
- `/way_point` (geometry_msgs/PointStamped): 当前目标路径点
- `/navi_result` (global_traj_generate/NavigationResult): 导航结果

## 路径点格式

在`src/global_traj_generate/data/point.txt`中定义路径点：

```
# 路径点文件格式：x,y,z
# 支持注释行（#开头）
2.5, 4.33, 0.0
7.0, 0.1, 0.0
9.0, 0.02, 0.0
```

## 系统特性

### 安全特性
- 里程计健康监测和异常处理
- 位置跳跃检测和恢复机制
- 障碍物检测和自动避障
- 多层安全停车机制

### 控制特性
- 双PID控制器（线速度/角速度）
- 积分饱和保护
- 平滑控制过渡
- 自适应控制权限

### 调试特性
- 详细的日志输出
- 可配置的安全检查
- RViz可视化支持
- 运行时参数调整

## 主要改进点（相比1.0版本）

1. **架构分离**：轨迹规划与控制分离，职责更清晰
2. **模块化设计**：每个节点功能单一，便于维护
3. **标准接口**：使用标准ROS2消息和服务接口
4. **配置灵活**：支持运行时参数调整
5. **调试友好**：完善的日志和可视化支持

## 开发指南

### 添加新路径点
1. 编辑`src/global_traj_generate/data/point.txt`
2. 重启`global_traj_generate`节点

### 调整控制参数
1. 编辑`src/differential_tracked/config/navigator_params.yaml`
2. 重启`differential_tracked_navigator`节点

### 自定义轨迹规划
1. 修改`global_traj_generate_ros2.cpp`中的规划逻辑
2. 重新编译和部署

## 故障排除

### 常见问题
1. **节点无法启动**：检查依赖包是否正确安装
2. **无路径点数据**：检查point.txt文件路径和格式
3. **车辆不动**：检查里程计数据和安全检查配置
4. **控制不稳定**：调整PID参数或检查传感器数据质量

### 调试命令
```bash
# 查看话题状态
ros2 topic list
ros2 topic echo /state_estimation
ros2 topic echo /navi_result

# 查看节点信息
ros2 node list
ros2 node info /global_traj_generate

# 参数调试
ros2 param list /differential_tracked_navigator
ros2 param get /differential_tracked_navigator vehicle.max_linear_velocity
```

## 维护者

- 导航系统开发团队
- 版本：2.0（重构版）
- 最后更新：2024

---

更多详细信息请参考各包中的源代码注释和配置文件。