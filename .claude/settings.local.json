{"permissions": {"allow": ["mcp__thinking__sequentialthinking", "Bash(git stash:*)", "Bash(git revert:*)", "Bash(git add:*)", "<PERSON><PERSON>(chmod:*)", "Bash(colcon build:*)", "<PERSON><PERSON>(python3:*)", "Bash(ls:*)", "Bash(cp:*)", "<PERSON><PERSON>(source:*)", "Bash(ros2 launch:*)", "<PERSON><PERSON>(timeout:*)", "Bash(grep:*)", "Bash(rm:*)", "<PERSON><PERSON>(mv:*)", "Bash(find:*)"], "deny": [], "ask": []}}