#include <math.h>
#include <time.h>
#include <stdio.h>
#include <stdlib.h>
#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/point.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/path.hpp>
#include <tf2/LinearMath/Transform.h>
#include <tf2_ros/transform_broadcaster.h>
#include <sstream>
#include <fstream>
#include <vector>
#include <termios.h>
#include <unistd.h>
#include <fcntl.h>
#include <std_msgs/msg/int8.hpp>
#include <std_msgs/msg/empty.hpp>
#include <iostream>
#include <thread>
#include <chrono>
#include <string>

using namespace std;

// 路径点数据结构  
struct WaypointData {
    double x, y, z;
    int point_id;
};

// ROS2 节点类 - 路径点发布器（根据导航-To-APP接口调整）
class PointPublishNode : public rclcpp::Node
{
public:
    PointPublishNode() : Node("point_publish_node")
    {
        // === 初始化发布者 ===
        // 根据接口要求：发布/path(nav_msgs::Path)
        pubPath = this->create_publisher<nav_msgs::msg::Path>("/path", 10);
        
        // === 初始化订阅者 ===
        // 根据接口要求：接收/start_navigation启动导航
        subStartNavigation = this->create_subscription<std_msgs::msg::Int8>(
            "/start_navigation", 1, 
            std::bind(&PointPublishNode::startNavigationCallback, this, std::placeholders::_1));
        
        // 里程计数据订阅（用于状态监控）
        subOdometry = this->create_subscription<nav_msgs::msg::Odometry>(
            "/state_estimation", 1, 
            std::bind(&PointPublishNode::odomCallback, this, std::placeholders::_1));
        
        // === 初始化参数 ===
        this->declare_parameter("waypoints_file", std::string("src/global_traj_generate/data/point.txt"));
        this->declare_parameter("publish_rate", 2.0);
        
        waypoints_file_ = this->get_parameter("waypoints_file").as_string();
        double publish_rate = this->get_parameter("publish_rate").as_double();
        
        // 初始化状态变量
        navigation_started_ = false;
        current_waypoint_index_ = 0;
        
        // 创建定时器用于发布路径点
        publish_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(static_cast<int>(1000.0 / publish_rate)),
            std::bind(&PointPublishNode::publishTimerCallback, this)
        );
        
        // 预加载路径点数据
        loadWaypoints();
        
        RCLCPP_INFO(this->get_logger(), 
                   "路径点发布器初始化完成 - 等待/start_navigation信号启动，加载了 %zu 个路径点", 
                   waypoints_.size());
    }

private:
    // === ROS接口 ===
    rclcpp::Publisher<nav_msgs::msg::Path>::SharedPtr pubPath;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr subStartNavigation;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr subOdometry;
    rclcpp::TimerBase::SharedPtr publish_timer_;
    
    // === 状态变量 ===
    std::vector<WaypointData> waypoints_;
    size_t current_waypoint_index_;
    bool navigation_started_;
    double odomx_ = 0, odomy_ = 0, odomz_ = 0;
    std::string waypoints_file_;
    
    void startNavigationCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // 根据接口要求：接收/start_navigation后开始读取和发布point
        (void)msg; // 避免未使用参数警告
        
        if (!navigation_started_ && !waypoints_.empty()) {
            navigation_started_ = true;
            current_waypoint_index_ = 0;
            
            RCLCPP_INFO(this->get_logger(), 
                       "收到导航启动信号 - 开始发布路径点序列，共 %zu 个路径点", 
                       waypoints_.size());
            
            // 立即发布第一个路径点
            publishCurrentWaypoint();
        } else if (waypoints_.empty()) {
            RCLCPP_ERROR(this->get_logger(), "无法启动导航 - 路径点数据为空");
        } else {
            RCLCPP_WARN(this->get_logger(), "导航已经启动，忽略重复的启动信号");
        }
    }
    
    void publishTimerCallback()
    {
        // 只有在收到启动信号后才发布路径点
        if (navigation_started_ && !waypoints_.empty()) {
            publishCurrentWaypoint();
            
            // 自动切换到下一个路径点
            if (current_waypoint_index_ < waypoints_.size() - 1) {
                current_waypoint_index_++;
            } else {
                // 所有路径点已发布完毕
                RCLCPP_INFO(this->get_logger(), "所有路径点已发布完毕");
                navigation_started_ = false; // 停止发布
            }
        }
    }
    
    void publishCurrentWaypoint()
    {
        if (current_waypoint_index_ >= waypoints_.size()) {
            return;
        }
        
        const auto& waypoint = waypoints_[current_waypoint_index_];
        
        // 根据接口要求：发布/path(nav_msgs::Path)
        nav_msgs::msg::Path path_msg;
        path_msg.header.stamp = this->get_clock()->now();
        path_msg.header.frame_id = "map";
        
        // 创建PoseStamped并添加到路径中
        geometry_msgs::msg::PoseStamped pose_stamped;
        pose_stamped.header = path_msg.header;
        
        // 设置位置
        pose_stamped.pose.position.x = waypoint.x;
        pose_stamped.pose.position.y = waypoint.y;
        pose_stamped.pose.position.z = waypoint.z;
        
        // 设置朝向（指向下一个路径点，如果有的话）
        if (current_waypoint_index_ + 1 < waypoints_.size()) {
            const auto& next_waypoint = waypoints_[current_waypoint_index_ + 1];
            double dx = next_waypoint.x - waypoint.x;
            double dy = next_waypoint.y - waypoint.y;
            double yaw = atan2(dy, dx);
            
            // 将偏航角转换为四元数
            pose_stamped.pose.orientation.w = cos(yaw / 2);
            pose_stamped.pose.orientation.z = sin(yaw / 2);
            pose_stamped.pose.orientation.x = 0.0;
            pose_stamped.pose.orientation.y = 0.0;
        } else {
            // 最后一个点，保持默认朝向
            pose_stamped.pose.orientation.w = 1.0;
            pose_stamped.pose.orientation.x = 0.0;
            pose_stamped.pose.orientation.y = 0.0;
            pose_stamped.pose.orientation.z = 0.0;
        }
        
        // 将当前路径点添加到路径中
        path_msg.poses.push_back(pose_stamped);
        
        pubPath->publish(path_msg);
        
        RCLCPP_INFO(this->get_logger(), 
                   "发布路径点 %zu/%zu: (%.2f, %.2f, %.2f)", 
                   current_waypoint_index_ + 1, waypoints_.size(),
                   waypoint.x, waypoint.y, waypoint.z);
    }
    
    void loadWaypoints()
    {
        waypoints_.clear();
        std::ifstream file(waypoints_file_);
        
        if (!file.is_open()) {
            RCLCPP_ERROR(this->get_logger(), "无法打开路径点文件: %s", waypoints_file_.c_str());
            return;
        }

        std::string line;
        int point_id = 0;
        
        while (std::getline(file, line)) {
            // 跳过空行和注释行
            if (line.empty() || line[0] == '#') {
                continue;
            }
            
            // 解析坐标（格式：x,y,z 或 x,y）
            std::istringstream iss(line);
            std::string token;
            std::vector<std::string> tokens;
            
            while (std::getline(iss, token, ',')) {
                // 去除空格
                token.erase(0, token.find_first_not_of(" \t"));
                token.erase(token.find_last_not_of(" \t") + 1);
                tokens.push_back(token);
            }
            
            if (tokens.size() >= 2) {
                try {
                    WaypointData wp;
                    wp.x = std::stod(tokens[0]);
                    wp.y = std::stod(tokens[1]);
                    wp.z = (tokens.size() > 2) ? std::stod(tokens[2]) : 0.0;
                    wp.point_id = point_id++;
                    
                    waypoints_.push_back(wp);
                    
                    RCLCPP_DEBUG(this->get_logger(), "加载路径点 %d: (%.2f, %.2f, %.2f)", 
                               wp.point_id, wp.x, wp.y, wp.z);
                } catch (const std::exception& e) {
                    RCLCPP_WARN(this->get_logger(), "解析路径点失败: %s", line.c_str());
                }
            }
        }
        
        file.close();
        RCLCPP_INFO(this->get_logger(), "从文件 %s 加载了 %zu 个路径点", 
                   waypoints_file_.c_str(), waypoints_.size());
    }
    
    void odomCallback(const nav_msgs::msg::Odometry::SharedPtr msg)
    {
        // 更新当前位置信息（用于状态监控）
        odomx_ = msg->pose.pose.position.x;
        odomy_ = msg->pose.pose.position.y;
        odomz_ = msg->pose.pose.position.z;
    }
};

// Temporary simple main function for compilation
int main(int argc, char** argv)
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<PointPublishNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}