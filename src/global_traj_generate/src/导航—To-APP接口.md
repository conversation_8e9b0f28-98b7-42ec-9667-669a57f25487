1、导航—To-APP

车到是否到达目的地（所有目标点导航完成）：
  Topic：/stopAtTarget,
  类型：std_msgs::Int8 

车遇到障碍物停止状态：
  Topic：/stop,
  类型：std_msgs::Int8 

规划路径上报：
    Topic：/path
    类型：nav_msgs::msg::Path

车辆偏移量：
    Topic: /vehicle/lateral_deviation
    类型：LateralDeviation.msg

     # LateralDeviation.msg 定义
     string reference_path_id   # 可选：参考轨迹 ID
     float32 lateral_error_m    # 横向误差（米）
     float32 heading_error_rad  # 航向误差（弧度，可选）
     
导航成功状态：
  Topic：/navigation_success,
  类型：std_msgs::Int8

2、APP-To-导航

重规划（本项目不涉及）：
  Topic：/replan,
  类型：std_msgs::Int8 

启动导航：
  Topic：/start_navigation,
  类型：std_msgs::Int8 

终止导航：
  Topic：/stop_navigation,
  类型：std_msgs::Int8  
    
导航目标点下发（本项目不涉及）：
    Topic：/web_goal_pose
    类型：geometry_msgs::PoseStamped    
