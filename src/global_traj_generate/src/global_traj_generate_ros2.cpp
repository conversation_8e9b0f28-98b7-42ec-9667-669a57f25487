#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/empty.hpp>
#include <std_msgs/msg/int8.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

#include <Eigen/Dense>

#include <sensor_msgs/msg/point_cloud2.hpp>
#include <sensor_msgs/msg/imu.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <geometry_msgs/msg/pose2_d.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/quaternion.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/path.hpp>

#include "global_traj_generate/msg/navigation_result.hpp"
#include "global_traj_generate/msg/navigation_target.hpp"
#include "global_traj_generate/msg/lateral_deviation.hpp"

#include <fstream>
#include <sstream>
#include <vector>
#include <string>
#include <limits>
#include <cmath>

using namespace std;
constexpr double PI = 3.14159265;

// 全局轨迹生成器类（根据导航-To-APP接口调整）
class GlobalTrajGenerate : public rclcpp::Node
{
public:
    GlobalTrajGenerate() : Node("global_traj_generate")
    {
        // === 初始化订阅者 ===
        // 根据接口要求：接收/path获取路径点
        sub_path_ = this->create_subscription<nav_msgs::msg::Path>(
            "/path", 10, std::bind(&GlobalTrajGenerate::pathCallback, this, std::placeholders::_1));
        
        // 根据接口要求：接收/stop_navigation停止导航
        sub_stop_navigation_ = this->create_subscription<std_msgs::msg::Int8>(
            "/stop_navigation", 10, std::bind(&GlobalTrajGenerate::stopNavigationCallback, this, std::placeholders::_1));
        
        // 接收里程计数据获取当前位置
        sub_laser_odometry_ = this->create_subscription<nav_msgs::msg::Odometry>(
            "/state_estimation", 10, std::bind(&GlobalTrajGenerate::laserOdometryCallback, this, std::placeholders::_1));
        
        // === 初始化发布者 ===
        // 根据接口要求：发布/local_goal(geometry_msgs::PoseStamped)
        pub_local_goal_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("/local_goal", 10);
        
        // 根据接口要求：发布/stopAtTarget当车辆到达目的地
        pub_stop_at_target_ = this->create_publisher<std_msgs::msg::Int8>("/stopAtTarget", 10);
        
        // 横向偏移监控发布者
        pub_lateral_deviation_ = this->create_publisher<global_traj_generate::msg::LateralDeviation>("/vehicle/lateral_deviation", 10);
        
        // === 初始化参数 ===
        this->declare_parameter("goal_tolerance", 0.3);
        this->declare_parameter("trajectory_update_rate", 10.0);
        this->declare_parameter("lookahead_distance", 1.0);
        this->declare_parameter("lateral_deviation_threshold", 10.0);
        
        goal_tolerance_ = this->get_parameter("goal_tolerance").as_double();
        double update_rate = this->get_parameter("trajectory_update_rate").as_double();
        lookahead_distance_ = this->get_parameter("lookahead_distance").as_double();
        lateral_deviation_threshold_ = this->get_parameter("lateral_deviation_threshold").as_double();
        
        // === 初始化状态变量 ===
        navigation_active_ = false;
        current_target_reached_ = false;
        
        // 创建定时器用于定期检查和发布局部目标
        trajectory_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(static_cast<int>(1000.0 / update_rate)),
            std::bind(&GlobalTrajGenerate::trajectoryUpdateCallback, this)
        );
        
        RCLCPP_INFO(this->get_logger(), 
                   "全局轨迹生成器初始化完成 - 等待/path路径点数据，目标容差: %.2fm，横向偏移阈值: %.2fm", 
                   goal_tolerance_, lateral_deviation_threshold_);
    }

private:
    // === ROS接口 ===
    rclcpp::Subscription<nav_msgs::msg::Path>::SharedPtr sub_path_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr sub_stop_navigation_;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr sub_laser_odometry_;
    
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pub_local_goal_;
    rclcpp::Publisher<std_msgs::msg::Int8>::SharedPtr pub_stop_at_target_;
    rclcpp::Publisher<global_traj_generate::msg::LateralDeviation>::SharedPtr pub_lateral_deviation_;
    
    rclcpp::TimerBase::SharedPtr trajectory_timer_;
    
    // === 状态变量 ===
    nav_msgs::msg::Odometry current_odom_;
    geometry_msgs::msg::PoseStamped current_target_;
    nav_msgs::msg::Path current_path_;  // 存储完整路径
    size_t current_waypoint_index_ = 0;  // 当前路径点索引
    bool navigation_active_;
    bool current_target_reached_;
    bool has_current_odom_ = false;
    bool has_current_target_ = false;
    
    // === 参数 ===
    double goal_tolerance_;
    double lookahead_distance_;
    double lateral_deviation_threshold_;
    
    void pathCallback(const nav_msgs::msg::Path::SharedPtr msg)
    {
        // 根据接口要求：接收/path路径点数据进行顺序导航
        if (msg->poses.empty()) {
            RCLCPP_WARN(this->get_logger(), "接收到空的路径数据");
            return;
        }
        
        // 检查是否为相同路径，避免重复重置导航进度
        bool is_same_path = false;
        if (!current_path_.poses.empty() && current_path_.poses.size() == msg->poses.size()) {
            is_same_path = true;
            for (size_t i = 0; i < msg->poses.size(); i++) {
                const auto& old_pose = current_path_.poses[i].pose;
                const auto& new_pose = msg->poses[i].pose;
                double dx = old_pose.position.x - new_pose.position.x;
                double dy = old_pose.position.y - new_pose.position.y;
                if (sqrt(dx*dx + dy*dy) > 0.01) { // 1cm tolerance
                    is_same_path = false;
                    break;
                }
            }
        }
        
        if (is_same_path) {
            RCLCPP_DEBUG(this->get_logger(), "收到相同路径，保持当前导航进度");
            return;
        }
        
        // 存储新路径
        current_path_ = *msg;
        
        // 只在收到新路径或导航未激活时重置索引
        if (!navigation_active_) {
            current_waypoint_index_ = 0;
            current_target_ = msg->poses[0];
            has_current_target_ = true;
            current_target_reached_ = false;
            
            // 启动导航
            navigation_active_ = true;
            RCLCPP_INFO(this->get_logger(), 
                       "收到新路径数据，开始顺序导航 - 共 %zu 个路径点，当前目标: (%.2f, %.2f, %.2f)", 
                       msg->poses.size(),
                       msg->poses[0].pose.position.x, msg->poses[0].pose.position.y, msg->poses[0].pose.position.z);
            
            // 立即发布局部目标
            publishLocalGoal();
        } else {
            // 导航激活时收到新路径，保持当前索引但验证有效性
            if (current_waypoint_index_ >= msg->poses.size()) {
                // 当前索引超出新路径范围，重置到最后一个有效索引
                current_waypoint_index_ = msg->poses.size() - 1;
                RCLCPP_WARN(this->get_logger(), "当前索引超出新路径范围，调整到路径点 %zu", current_waypoint_index_ + 1);
            }
            
            // 更新当前目标到调整后的索引位置
            current_target_ = msg->poses[current_waypoint_index_];
            has_current_target_ = true;
            current_target_reached_ = false;
            
            RCLCPP_INFO(this->get_logger(), 
                       "更新路径数据 - 共 %zu 个路径点，继续导航到路径点 %zu: (%.2f, %.2f)", 
                       msg->poses.size(), current_waypoint_index_ + 1,
                       current_target_.pose.position.x, current_target_.pose.position.y);
            
            // 立即发布更新后的局部目标
            publishLocalGoal();
        }
    }
    
    void stopNavigationCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // 根据接口要求：接收/stop_navigation停止发布导航数据
        (void)msg; // 避免未使用参数警告
        
        if (navigation_active_) {
            navigation_active_ = false;
            has_current_target_ = false;
            RCLCPP_INFO(this->get_logger(), "收到停止导航信号 - 停止发布导航数据");
        } else {
            RCLCPP_INFO(this->get_logger(), "收到停止导航信号，但导航未激活");
        }
    }
    
    void laserOdometryCallback(const nav_msgs::msg::Odometry::SharedPtr msg)
    {
        current_odom_ = *msg;
        has_current_odom_ = true;
    }

    void trajectoryUpdateCallback()
    {
        // 检查导航状态和必要条件
        if (!navigation_active_ || !has_current_target_ || !has_current_odom_) {
            return;
        }
        
        // 检查是否到达目标点
        if (hasReachedTarget()) {
            if (!current_target_reached_) {
                // 刚到达当前目标点
                current_target_reached_ = true;
                
                RCLCPP_INFO(this->get_logger(), 
                           "已到达路径点 %zu/%zu: (%.2f, %.2f)", 
                           current_waypoint_index_ + 1, current_path_.poses.size(),
                           current_target_.pose.position.x, current_target_.pose.position.y);
                
                // 检查是否还有下一个路径点
                if (current_waypoint_index_ + 1 < current_path_.poses.size()) {
                    // 切换到下一个路径点
                    current_waypoint_index_++;
                    current_target_ = current_path_.poses[current_waypoint_index_];
                    current_target_reached_ = false;
                    
                    RCLCPP_INFO(this->get_logger(), 
                               "切换到下一个路径点 %zu/%zu: (%.2f, %.2f)", 
                               current_waypoint_index_ + 1, current_path_.poses.size(),
                               current_target_.pose.position.x, current_target_.pose.position.y);
                    
                    // 发布新的局部目标
                    publishLocalGoal();
                } else {
                    // 所有路径点都已到达
                    std_msgs::msg::Int8 stop_msg;
                    stop_msg.data = 1;  // 1表示到达目标停车
                    pub_stop_at_target_->publish(stop_msg);
                    
                    RCLCPP_INFO(this->get_logger(), 
                               "所有路径点导航完成！总共 %zu 个路径点", 
                               current_path_.poses.size());
                    
                    // 停止导航
                    navigation_active_ = false;
                    return;
                }
            }
        } else {
            // 未到达目标，继续发布局部目标
            current_target_reached_ = false;
            publishLocalGoal();
            
            // 检查横向偏移
            checkLateralDeviation();
        }
    }

    bool hasReachedTarget()
    {
        if (!has_current_target_ || !has_current_odom_) {
            return false;
        }

        double dx = current_target_.pose.position.x - current_odom_.pose.pose.position.x;
        double dy = current_target_.pose.position.y - current_odom_.pose.pose.position.y;
        double distance = sqrt(dx * dx + dy * dy);
        
        return distance < goal_tolerance_;
    }
    
    // 计算四元数到yaw角的转换
    double getYawFromQuaternion(const geometry_msgs::msg::Quaternion& q)
    {
        double siny_cosp = 2.0 * (q.w * q.z + q.x * q.y);
        double cosy_cosp = 1.0 - 2.0 * (q.y * q.y + q.z * q.z);
        return std::atan2(siny_cosp, cosy_cosp);
    }
    
    void checkLateralDeviation()
    {
        // 检查横向偏移（使用叉积方法计算真正的横向偏差）
        if (!has_current_target_ || !has_current_odom_ || current_path_.poses.empty()) {
            return;
        }
        
        // 当前车辆位置和朝向
        double x = current_odom_.pose.pose.position.x;
        double y = current_odom_.pose.pose.position.y;
        double theta = getYawFromQuaternion(current_odom_.pose.pose.orientation);
        
        // 找到最近的轨迹点
        double min_dist = std::numeric_limits<double>::max();
        int nearest_idx = 0;
        for (size_t i = 0; i < current_path_.poses.size(); i++) {
            double dx = x - current_path_.poses[i].pose.position.x;
            double dy = y - current_path_.poses[i].pose.position.y;
            double dist = dx * dx + dy * dy;
            if (dist < min_dist) {
                min_dist = dist;
                nearest_idx = i;
            }
        }
        
        auto nearest_point = current_path_.poses[nearest_idx].pose.position;
        
        // 计算参考方向（通过下一个点近似）
        int next_idx = std::min(nearest_idx + 1, (int)current_path_.poses.size() - 1);
        auto next_point = current_path_.poses[next_idx].pose.position;
        double theta_ref = std::atan2(next_point.y - nearest_point.y, next_point.x - nearest_point.x);
        
        // 横向偏移 e_y (叉积方法)
        double dx = x - nearest_point.x;
        double dy = y - nearest_point.y;
        double lateral_error = dx * std::sin(theta_ref) - dy * std::cos(theta_ref);
        
        // 航向误差 e_theta
        double heading_error = theta - theta_ref;
        // wrap 到 [-pi, pi]
        while (heading_error > M_PI) heading_error -= 2.0 * M_PI;
        while (heading_error < -M_PI) heading_error += 2.0 * M_PI;
        
        // 横向偏移的绝对值用于阈值判断
        double abs_lateral_error = std::abs(lateral_error);
        
        // 检查是否超过横向偏移阈值
        if (abs_lateral_error > lateral_deviation_threshold_) {
            // 发布横向偏移信号
            global_traj_generate::msg::LateralDeviation deviation_msg;
            deviation_msg.reference_path_id = std::to_string(nearest_idx);
            deviation_msg.lateral_error_m = static_cast<float>(lateral_error);
            deviation_msg.heading_error_rad = static_cast<float>(heading_error);
            pub_lateral_deviation_->publish(deviation_msg);
          /*  
            RCLCPP_WARN(this->get_logger(),
                       "检测到横向偏移过大: %.3fm (阈值: %.2fm) 航向误差: %.3frad - "
                       "当前位置: (%.2f, %.2f) 最近轨迹点: (%.2f, %.2f) 索引: %d",
                       lateral_error, lateral_deviation_threshold_, heading_error,
                       x, y, nearest_point.x, nearest_point.y, nearest_idx);
          */             
        }
    }
    
    void publishLocalGoal()
    {
        if (!has_current_target_) {
            return;
        }
        
        // 根据接口要求：发布/local_goal(geometry_msgs::PoseStamped)
        geometry_msgs::msg::PoseStamped local_goal = current_target_;
        
        // 更新时间戳
        local_goal.header.stamp = this->get_clock()->now();
        
        pub_local_goal_->publish(local_goal);
        
        RCLCPP_DEBUG(this->get_logger(), 
                    "发布局部目标: (%.2f, %.2f, %.2f)", 
                    local_goal.pose.position.x, 
                    local_goal.pose.position.y, 
                    local_goal.pose.position.z);
    }
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<GlobalTrajGenerate>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}