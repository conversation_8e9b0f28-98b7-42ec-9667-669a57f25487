#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/string.hpp>
#include <std_msgs/msg/empty.hpp>
#include <std_msgs/msg/int8.hpp>
#include <tf2_ros/transform_broadcaster.h>
#include <tf2_geometry_msgs/tf2_geometry_msgs.hpp>

#include <Eigen/Dense>

#include <sensor_msgs/msg/point_cloud2.hpp>
#include <sensor_msgs/msg/imu.hpp>
#include <sensor_msgs/msg/laser_scan.hpp>
#include <geometry_msgs/msg/pose2_d.hpp>
#include <geometry_msgs/msg/pose_stamped.hpp>
#include <geometry_msgs/msg/quaternion.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/path.hpp>

#include "global_traj_generate/msg/navigation_result.hpp"
#include "global_traj_generate/msg/navigation_target.hpp"

#include <fstream>
#include <sstream>
#include <vector>
#include <string>

using namespace std;
constexpr double PI = 3.14159265;

// 全局轨迹生成器类（根据导航-To-APP接口调整）
class GlobalTrajGenerate : public rclcpp::Node
{
public:
    GlobalTrajGenerate() : Node("global_traj_generate")
    {
        // === 初始化订阅者 ===
        // 根据接口要求：接收/path获取路径点
        sub_path_ = this->create_subscription<nav_msgs::msg::Path>(
            "/path", 10, std::bind(&GlobalTrajGenerate::pathCallback, this, std::placeholders::_1));
        
        // 根据接口要求：接收/stop_navigation停止导航
        sub_stop_navigation_ = this->create_subscription<std_msgs::msg::Int8>(
            "/stop_navigation", 10, std::bind(&GlobalTrajGenerate::stopNavigationCallback, this, std::placeholders::_1));
        
        // 接收里程计数据获取当前位置
        sub_laser_odometry_ = this->create_subscription<nav_msgs::msg::Odometry>(
            "/state_estimation", 10, std::bind(&GlobalTrajGenerate::laserOdometryCallback, this, std::placeholders::_1));
        
        // === 初始化发布者 ===
        // 根据接口要求：发布/local_goal(geometry_msgs::PoseStamped)
        pub_local_goal_ = this->create_publisher<geometry_msgs::msg::PoseStamped>("/local_goal", 10);
        
        // 根据接口要求：发布/stopAtTarget当车辆到达目的地
        pub_stop_at_target_ = this->create_publisher<std_msgs::msg::Empty>("/stopAtTarget", 10);
        
        // === 初始化参数 ===
        this->declare_parameter("goal_tolerance", 0.5);
        this->declare_parameter("trajectory_update_rate", 10.0);
        this->declare_parameter("lookahead_distance", 1.0);
        
        goal_tolerance_ = this->get_parameter("goal_tolerance").as_double();
        double update_rate = this->get_parameter("trajectory_update_rate").as_double();
        lookahead_distance_ = this->get_parameter("lookahead_distance").as_double();
        
        // === 初始化状态变量 ===
        navigation_active_ = false;
        current_target_reached_ = false;
        
        // 创建定时器用于定期检查和发布局部目标
        trajectory_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(static_cast<int>(1000.0 / update_rate)),
            std::bind(&GlobalTrajGenerate::trajectoryUpdateCallback, this)
        );
        
        RCLCPP_INFO(this->get_logger(), 
                   "全局轨迹生成器初始化完成 - 等待/path路径点数据，目标容差: %.2fm", 
                   goal_tolerance_);
    }

private:
    // === ROS接口 ===
    rclcpp::Subscription<nav_msgs::msg::Path>::SharedPtr sub_path_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr sub_stop_navigation_;
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr sub_laser_odometry_;
    
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pub_local_goal_;
    rclcpp::Publisher<std_msgs::msg::Empty>::SharedPtr pub_stop_at_target_;
    
    rclcpp::TimerBase::SharedPtr trajectory_timer_;
    
    // === 状态变量 ===
    nav_msgs::msg::Odometry current_odom_;
    geometry_msgs::msg::PoseStamped current_target_;
    bool navigation_active_;
    bool current_target_reached_;
    bool has_current_odom_ = false;
    bool has_current_target_ = false;
    
    // === 参数 ===
    double goal_tolerance_;
    double lookahead_distance_;
    
    void pathCallback(const nav_msgs::msg::Path::SharedPtr msg)
    {
        // 根据接口要求：接收/path路径点数据
        if (msg->poses.empty()) {
            RCLCPP_WARN(this->get_logger(), "接收到空的路径数据");
            return;
        }
        
        // 使用第一个路径点作为当前目标（简单实现）
        current_target_ = msg->poses[0];
        has_current_target_ = true;
        current_target_reached_ = false;
        
        // 启动导航（如果还未启动）
        if (!navigation_active_) {
            navigation_active_ = true;
            RCLCPP_INFO(this->get_logger(), 
                       "收到路径数据，启动导航到目标: (%.2f, %.2f, %.2f)", 
                       msg->poses[0].pose.position.x, msg->poses[0].pose.position.y, msg->poses[0].pose.position.z);
        } else {
            RCLCPP_INFO(this->get_logger(), 
                       "更新导航目标到: (%.2f, %.2f, %.2f)", 
                       msg->poses[0].pose.position.x, msg->poses[0].pose.position.y, msg->poses[0].pose.position.z);
        }
        
        // 立即发布局部目标
        publishLocalGoal();
    }
    
    void stopNavigationCallback(const std_msgs::msg::Int8::SharedPtr msg)
    {
        // 根据接口要求：接收/stop_navigation停止发布导航数据
        (void)msg; // 避免未使用参数警告
        
        if (navigation_active_) {
            navigation_active_ = false;
            has_current_target_ = false;
            RCLCPP_INFO(this->get_logger(), "收到停止导航信号 - 停止发布导航数据");
        } else {
            RCLCPP_INFO(this->get_logger(), "收到停止导航信号，但导航未激活");
        }
    }
    
    void laserOdometryCallback(const nav_msgs::msg::Odometry::SharedPtr msg)
    {
        current_odom_ = *msg;
        has_current_odom_ = true;
    }

    void trajectoryUpdateCallback()
    {
        // 检查导航状态和必要条件
        if (!navigation_active_ || !has_current_target_ || !has_current_odom_) {
            return;
        }
        
        // 检查是否到达目标点
        if (hasReachedTarget()) {
            if (!current_target_reached_) {
                // 刚到达目标点
                current_target_reached_ = true;
                
                // 根据接口要求：车辆到达目的地后发布/stopAtTarget
                std_msgs::msg::Empty stop_msg;
                pub_stop_at_target_->publish(stop_msg);
                
                RCLCPP_INFO(this->get_logger(), 
                           "车辆已到达目标点 (%.2f, %.2f) - 发布停止信号", 
                           current_target_.pose.position.x, current_target_.pose.position.y);
                
                // 停止发布局部目标
                navigation_active_ = false;
                return;
            }
        } else {
            // 未到达目标，继续发布局部目标
            current_target_reached_ = false;
            publishLocalGoal();
        }
    }

    bool hasReachedTarget()
    {
        if (!has_current_target_ || !has_current_odom_) {
            return false;
        }

        double dx = current_target_.pose.position.x - current_odom_.pose.pose.position.x;
        double dy = current_target_.pose.position.y - current_odom_.pose.pose.position.y;
        double distance = sqrt(dx * dx + dy * dy);
        
        return distance < goal_tolerance_;
    }
    
    void publishLocalGoal()
    {
        if (!has_current_target_) {
            return;
        }
        
        // 根据接口要求：发布/local_goal(geometry_msgs::PoseStamped)
        geometry_msgs::msg::PoseStamped local_goal = current_target_;
        
        // 更新时间戳
        local_goal.header.stamp = this->get_clock()->now();
        
        pub_local_goal_->publish(local_goal);
        
        RCLCPP_DEBUG(this->get_logger(), 
                    "发布局部目标: (%.2f, %.2f, %.2f)", 
                    local_goal.pose.position.x, 
                    local_goal.pose.position.y, 
                    local_goal.pose.position.z);
    }
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<GlobalTrajGenerate>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}