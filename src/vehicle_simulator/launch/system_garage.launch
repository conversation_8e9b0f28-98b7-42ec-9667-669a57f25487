import os

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription, TimerAction
from launch.launch_description_sources import PythonLaunchDescriptionSource, FrontendLaunchDescriptionSource
from launch_ros.actions import Node
from launch.substitutions import LaunchConfiguration 

def generate_launch_description():
  world_name = LaunchConfiguration('world_name')
  vehicleHeight = LaunchConfiguration('vehicleHeight')
  cameraOffsetZ = LaunchConfiguration('cameraOffsetZ')
  vehicleX = LaunchConfiguration('vehicleX')
  vehicleY = LaunchConfiguration('vehicleY')
  vehicleZ = LaunchConfiguration('vehicleZ')
  terrainZ = LaunchConfiguration('terrainZ')
  vehicleYaw = LaunchConfiguration('vehicleYaw')
  gazebo_gui = LaunchConfiguration('gazebo_gui')
  checkTerrainConn = LaunchConfiguration('checkTerrainConn')
  
  declare_world_name = DeclareLaunchArgument('world_name', default_value='garage', description='')
  declare_vehicleHeight = DeclareLaunchArgument('vehicleHeight', default_value='0.75', description='')
  declare_cameraOffsetZ = DeclareLaunchArgument('cameraOffsetZ', default_value='0.0', description='')
  declare_vehicleX = DeclareLaunchArgument('vehicleX', default_value='0.0', description='')
  declare_vehicleY = DeclareLaunchArgument('vehicleY', default_value='0.0', description='')
  declare_vehicleZ = DeclareLaunchArgument('vehicleZ', default_value='0.0', description='')
  declare_terrainZ = DeclareLaunchArgument('terrainZ', default_value='0.0', description='')
  declare_vehicleYaw = DeclareLaunchArgument('vehicleYaw', default_value='0.0', description='')
  declare_gazebo_gui = DeclareLaunchArgument('gazebo_gui', default_value='false', description='')
  declare_checkTerrainConn = DeclareLaunchArgument('checkTerrainConn', default_value='true', description='')
  
  # 启动导航模块 - 替换原来的local_planner等模块
  start_navigation = IncludeLaunchDescription(
    PythonLaunchDescriptionSource(os.path.join(
      get_package_share_directory('differential_tracked'), 'launch', 'navigation_launch.py')
    ),
    launch_arguments={
      'use_sim_time': 'true',
    }.items()
  )

  # 启动全局轨迹生成器
  start_global_traj_generate = Node(
    package='global_traj_generate',
    executable='global_traj_generate_ros2',
    name='global_traj_generate',
    output='screen',
    parameters=[
      {'use_sim_time': True},
      {'goal_tolerance': 0.5},
      {'trajectory_update_rate': 10.0},
      {'lookahead_distance': 1.0},
      {'lateral_deviation_threshold': 2.0}
    ]
  )

  # 启动点发布器
  start_point_publisher = Node(
    package='point_publisher',
    executable='point_publish',
    name='point_publisher',
    output='screen',
    parameters=[
      {'use_sim_time': True}
    ]
  )

  start_vehicle_simulator = IncludeLaunchDescription(
    PythonLaunchDescriptionSource(os.path.join(
      get_package_share_directory('vehicle_simulator'), 'launch', 'vehicle_simulator.launch')
    ),
    launch_arguments={
      'world_name': world_name,
      'vehicleHeight': vehicleHeight,
      'cameraOffsetZ': cameraOffsetZ,
      'vehicleX': vehicleX,
      'vehicleY': vehicleY,
      'terrainZ': terrainZ,
      'vehicleYaw': vehicleYaw,
      'gui': gazebo_gui,
      'use_sim_time': 'true',
    }.items()
  )

  # 启用可视化工具
  start_visualization_tools = IncludeLaunchDescription(
    FrontendLaunchDescriptionSource(os.path.join(
      get_package_share_directory('visualization_tools'), 'launch', 'visualization_tools.launch')
    ),
    launch_arguments={
      'world_name': world_name,
    }.items()
  )

  start_joy = Node(
    package='joy', 
    executable='joy_node',
    name='ps3_joy',
    output='screen',
    parameters=[{
                'dev': "/dev/input/js0",
                'deadzone': 0.12,
                'autorepeat_rate': 0.0,
  		}]
  )

  rviz_config_file = os.path.join(get_package_share_directory('vehicle_simulator'), 'rviz', 'vehicle_simulator.rviz')
  start_rviz = Node(
    package='rviz2',
    executable='rviz2',
    arguments=['-d', rviz_config_file],
    output='screen'
  )

  delayed_start_rviz = TimerAction(
    period=8.0,
    actions=[
      start_rviz
    ]
  )

  ld = LaunchDescription()

  # Add the actions
  ld.add_action(declare_world_name)
  ld.add_action(declare_vehicleHeight)
  ld.add_action(declare_cameraOffsetZ)
  ld.add_action(declare_vehicleX)
  ld.add_action(declare_vehicleY)
  ld.add_action(declare_vehicleZ)
  ld.add_action(declare_terrainZ)
  ld.add_action(declare_vehicleYaw)
  ld.add_action(declare_gazebo_gui)
  ld.add_action(declare_checkTerrainConn)

  ld.add_action(start_navigation)
  ld.add_action(start_global_traj_generate)
  ld.add_action(start_point_publisher)
  ld.add_action(start_vehicle_simulator)
  ld.add_action(start_visualization_tools)
  ld.add_action(start_joy)
  ld.add_action(delayed_start_rviz)

  return ld
