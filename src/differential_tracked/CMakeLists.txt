cmake_minimum_required(VERSION 3.8)
project(differential_tracked)

# Default to C++17
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 17)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# Find dependencies
find_package(ament_cmake REQUIRED)
find_package(ament_cmake_python REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclpy REQUIRED)
find_package(geometry_msgs REQUIRED)
find_package(nav_msgs REQUIRED)
find_package(std_msgs REQUIRED)

# Include project headers
include_directories(include)

# Create C++ executable for navigation controller (PathFollower版本)
add_executable(differential_tracked_navigator
  src/differential_tracked_navigator.cpp
)

# Set dependencies for C++ executable
ament_target_dependencies(differential_tracked_navigator
  rclcpp
  geometry_msgs
  nav_msgs
  std_msgs
)

# Install C++ executable
install(TARGETS
  differential_tracked_navigator
  DESTINATION lib/${PROJECT_NAME}
)

# Python版本已被PathFollower C++版本替代
# install(PROGRAMS
#   differential_tracked_navigator/differential_tracked_navigator.py
#   DESTINATION lib/${PROJECT_NAME}
#   RENAME differential_tracked_navigator_python
# )

# Install launch files
install(DIRECTORY
  launch
  DESTINATION share/${PROJECT_NAME}/
)

# Install config files
install(DIRECTORY
  config
  DESTINATION share/${PROJECT_NAME}/
)

# Install maps folder
install(DIRECTORY
  maps
  DESTINATION share/${PROJECT_NAME}/
)

# Install RViz config
install(DIRECTORY
  rviz
  DESTINATION share/${PROJECT_NAME}/
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()