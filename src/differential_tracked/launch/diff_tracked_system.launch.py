#!/usr/bin/env python3
"""
差分履带车导航系统启动文件
==========================

这个启动文件用于启动完整的差分履带车导航系统，包括：
1. global_traj_generate - 全局轨迹规划器，从point文件生成导航目标
2. differential_tracked_navigator - 差分履带车控制器，执行PID控制
3. pointPublish - 路径点发布器，用于外部控制或手动测试

数据流向：
/start_navigation -> pointPublish -> /path -> global_traj_generate -> /local_goal -> differential_tracked_navigator -> cmd_vel
                                                                                  ↑
                                                                       state_estimation (里程计数据)

作者：导航系统开发团队
版本：2.0 - 重构版，分离轨迹规划和控制
"""

import os
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo, TimerAction
from launch.substitutions import LaunchConfiguration
from launch.conditions import IfCondition
from launch_ros.actions import Node
from ament_index_python.packages import get_package_share_directory


def generate_launch_description():
    """生成差分履带车导航系统启动描述"""
    
    # === 获取包路径 ===
    differential_tracked_pkg = get_package_share_directory('differential_tracked')
    global_traj_pkg = get_package_share_directory('global_traj_generate')
    point_publisher_pkg = get_package_share_directory('Point_Publisher')
    
    # === 声明启动参数 ===
    
    # 配置文件路径
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=os.path.join(differential_tracked_pkg, 'config', 'navigator_params.yaml'),
        description='差分履带导航器配置文件路径'
    )
    
    # 路径点文件路径
    waypoints_file_arg = DeclareLaunchArgument(
        'waypoints_file',
        default_value=os.path.join(global_traj_pkg, 'data', 'point.txt'),
        description='路径点数据文件路径'
    )
    
    # 地图文件路径
    map_file_arg = DeclareLaunchArgument(
        'map_file',
        #default_value=os.path.join(differential_tracked_pkg, 'maps', 'map.yaml'),
        default_value=os.path.join(
            get_package_share_directory('differential_tracked'), 'maps', 'map.yaml'
        ),
        description='地图文件路径'
    )
    
    # 系统模式选择
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='是否使用仿真时间'
    )
    
    # 启用路径点发布器
    enable_point_publisher_arg = DeclareLaunchArgument(
        'enable_point_publisher',
        default_value='True',
        description='是否启用路径点发布器（用于外部控制）'
    )
    
    # 启用可视化
    enable_rviz_arg = DeclareLaunchArgument(
        'enable_rviz',
        default_value='True',
        description='是否启动RViz可视化'
    )
    
    # === 节点配置 ===
    
    # 全局轨迹规划器节点
    global_traj_generate_node = Node(
        package='global_traj_generate',
        executable='global_traj_generate_ros2',
        name='global_traj_generate',
        output='screen',
        parameters=[
            {
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'waypoints_file': LaunchConfiguration('waypoints_file'),
                'goal_tolerance': 0.5,
                'trajectory_update_rate': 10.0,
            }
        ],
        remappings=[
            # 话题重映射
            ('/state_estimation', '/state_estimation'),
            ('/path', '/path'),
            ('/local_goal', '/local_goal'),
            ('/stopAtTarget', '/stopAtTarget'),
            ('/stop_navigation', '/stop_navigation'),
        ]
    )
    
    # 差分履带导航控制器节点
    differential_tracked_navigator_node = Node(
        package='differential_tracked',
        executable='differential_tracked_navigator',
        name='differential_tracked_navigator',
        output='screen',
        parameters=[
            LaunchConfiguration('config_file'),  # 从文件加载参数
            {
                'use_sim_time': LaunchConfiguration('use_sim_time'),
            }
        ],
        remappings=[
            # 话题重映射
            ('/state_estimation', '/state_estimation'),
            ('/registered_scan', '/registered_scan'),
            ('/map', '/map'),
            ('/cmd_vel', '/cmd_vel'),
            ('/way_point', '/way_point'),
            ('/local_goal', '/local_goal'),
            ('/stop_navigation', '/stop_navigation'),
            ('/stopAtTarget', '/stopAtTarget'),
            ('/stop', '/stop'),
            ('/vehicle/lateral_deviation', '/vehicle/lateral_deviation'),
        ]
    )
    
    # 静态坐标变换发布器 - map到odom
    map_to_odom_transform = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='map_to_odom',
        arguments=['--x', '0', '--y', '0', '--z', '0', 
                  '--yaw', '0', '--pitch', '0', '--roll', '0', 
                  '--frame-id', 'map', '--child-frame-id', 'odom'],
        parameters=[{'use_sim_time': LaunchConfiguration('use_sim_time')}]
    )
    
    # 静态坐标变换发布器 - odom到base_link
    odom_to_base_link_transform = Node(
        package='tf2_ros',
        executable='static_transform_publisher',
        name='odom_to_base_link',
        arguments=['--x', '0', '--y', '0', '--z', '0', 
                  '--yaw', '0', '--pitch', '0', '--roll', '0', 
                  '--frame-id', 'odom', '--child-frame-id', 'base_link'],
        parameters=[{'use_sim_time': LaunchConfiguration('use_sim_time')}]
    )
    
    # 路径点发布器节点（可选）
    point_publisher_node = Node(
        package='Point_Publisher',
        executable='pointPublish',
        name='point_publish_node',
        output='screen',
        parameters=[
            {
                'use_sim_time': LaunchConfiguration('use_sim_time'),
                'waypoints_file': LaunchConfiguration('waypoints_file'),
                'publish_rate': 1.0,
                'auto_publish': False,  # 手动控制模式
            }
        ],
        remappings=[
            ('/state_estimation', '/state_estimation'),
            ('/start_navigation', '/start_navigation'),
            ('/path', '/path'),
        ],
        condition=IfCondition(LaunchConfiguration('enable_point_publisher'))
    )
    
    # 地图服务器节点
    # Map server node (using reference implementation)
    map_server_node = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[{
            'yaml_filename': LaunchConfiguration('map_file'),
            'use_sim_time': LaunchConfiguration('use_sim_time')
        }]
    )
    
    # Lifecycle manager to manage map server (using reference implementation)
    lifecycle_manager_node = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_map',
        output='screen',
        parameters=[{
            'use_sim_time': LaunchConfiguration('use_sim_time'),
            'autostart': True,
            'node_names': ['map_server']
        }]
    )
    
    # RViz可视化节点（可选）
    rviz_config_file = os.path.join(differential_tracked_pkg, 'rviz', 'navigation.rviz')
    rviz_node = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        arguments=['-d', rviz_config_file],
        parameters=[
            {
                'use_sim_time': LaunchConfiguration('use_sim_time'),
            }
        ],
        condition=IfCondition(LaunchConfiguration('enable_rviz')),
        output='screen'
    )
    
    # 延迟启动RViz以确保其他节点就绪
    delayed_start_rviz = TimerAction(
        period=3.0,
        actions=[rviz_node]
    )
    
    # === 启动信息 ===
    launch_info = LogInfo(
        msg="启动差分履带车导航系统（新接口架构）:\n" +
            "  - 路径点发布器: pointPublish (接收/start_navigation，发布/path)\n" +
            "  - 全局轨迹规划器: global_traj_generate (接收/path，发布/local_goal)\n" +
            "  - 差分履带导航器: differential_tracked_navigator (接收/local_goal，发布控制和状态)\n" +
            "  - 地图服务器: map_server\n" +
            "数据流: /start_navigation -> pointPublish -> /path -> global_traj_generate -> /local_goal -> differential_tracked_navigator -> cmd_vel"
    )
    
    # === 生成启动描述 ===
    launch_description = LaunchDescription([
        # 参数声明
        config_file_arg,
        waypoints_file_arg,
        map_file_arg,
        use_sim_time_arg,
        enable_point_publisher_arg,
        enable_rviz_arg,
        
        # 启动信息
        launch_info,
        
        # 核心节点
        map_server_node,
        lifecycle_manager_node,
        global_traj_generate_node,
        differential_tracked_navigator_node,
        map_to_odom_transform,
        odom_to_base_link_transform,
        
        # 可选节点
        point_publisher_node,
        delayed_start_rviz,
    ])
    
    return launch_description


if __name__ == '__main__':
    """直接运行时的测试函数"""
    import sys
    print("差分履带车导航系统启动文件")
    print("用法: ros2 launch differential_tracked diff_tracked_system.launch.py")
    print("参数:")
    print("  config_file:=<配置文件路径>")
    print("  waypoints_file:=<路径点文件路径>")
    print("  map_file:=<地图文件路径>")
    print("  use_sim_time:=<true/false>")
    print("  enable_point_publisher:=<true/false>")
    print("  enable_rviz:=<true/false>")
    print("\n系统架构（新接口）:")
    print("  /start_navigation -> pointPublish -> /path -> global_traj_generate -> /local_goal -> differential_tracked_navigator -> cmd_vel")
    print("                                                                                                      ↑")
    print("                                                                                           state_estimation")