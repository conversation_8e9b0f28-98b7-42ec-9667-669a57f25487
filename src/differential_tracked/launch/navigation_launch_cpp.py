#!/usr/bin/env python3
"""
差分履带车导航启动文件 - 支持Python和C++版本
=============================================

这个启动文件可以启动Python或C++版本的差分履带车导航控制器。
默认使用C++版本以获得更好的性能。

参数:
    use_cpp_version (bool): 使用C++版本 (默认: true)
    config_file (str): 参数配置文件路径
    waypoints_file (str): 路径点文件路径  
    map_file (str): 地图文件路径
    use_sim_time (bool): 使用仿真时间 (默认: false)
"""

from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, LogInfo
from launch.conditions import IfCondition, UnlessCondition
from launch.substitutions import LaunchConfiguration, PathJoinSubstitution
from launch_ros.actions import Node
from launch_ros.substitutions import FindPackageShare
import os

def generate_launch_description():
    # 获取包路径
    pkg_share = FindPackageShare('differential_tracked')
    
    # 声明启动参数
    declare_use_cpp_version = DeclareLaunchArgument(
        'use_cpp_version',
        default_value='true',
        description='Use C++ version of the navigator (default: true for better performance)'
    )
    
    declare_config_file = DeclareLaunchArgument(
        'config_file',
        default_value=PathJoinSubstitution([pkg_share, 'config', 'navigator_params.yaml']),
        description='Navigation parameters file path'
    )
    
    declare_waypoints_file = DeclareLaunchArgument(
        'waypoints_file', 
        default_value=PathJoinSubstitution([pkg_share, 'config', 'waypoints.txt']),
        description='Waypoints file path'
    )
    
    declare_map_file = DeclareLaunchArgument(
        'map_file',
        default_value=PathJoinSubstitution([pkg_share, 'maps', 'map.yaml']),
        description='Map file path for map_server'
    )
    
    declare_use_sim_time = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='Use simulation time'
    )
    
    # C++版本导航控制器节点
    cpp_navigator_node = Node(
        package='differential_tracked',
        executable='differential_tracked_navigator_cpp',
        name='differential_tracked_navigator',
        output='screen',
        parameters=[
            LaunchConfiguration('config_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=IfCondition(LaunchConfiguration('use_cpp_version'))
    )
    
    # Python版本导航控制器节点
    python_navigator_node = Node(
        package='differential_tracked',
        executable='differential_tracked_navigator',
        name='differential_tracked_navigator',
        output='screen',
        parameters=[
            LaunchConfiguration('config_file'),
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ],
        condition=UnlessCondition(LaunchConfiguration('use_cpp_version'))
    )
    
    # 地图服务器节点（如果需要）
    map_server_node = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[
            {'yaml_filename': LaunchConfiguration('map_file')},
            {'use_sim_time': LaunchConfiguration('use_sim_time')}
        ]
    )
    
    # 生命周期管理器（用于地图服务器）
    lifecycle_manager_node = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_navigation',
        output='screen',
        parameters=[
            {'node_names': ['map_server']},
            {'use_sim_time': LaunchConfiguration('use_sim_time')},
            {'autostart': True}
        ]
    )
    
    # 信息日志
    cpp_version_log = LogInfo(
        msg='启动C++版本的差分履带车导航控制器',
        condition=IfCondition(LaunchConfiguration('use_cpp_version'))
    )
    
    python_version_log = LogInfo(
        msg='启动Python版本的差分履带车导航控制器', 
        condition=UnlessCondition(LaunchConfiguration('use_cpp_version'))
    )
    
    return LaunchDescription([
        # 声明参数
        declare_use_cpp_version,
        declare_config_file,
        declare_waypoints_file,
        declare_map_file,
        declare_use_sim_time,
        
        # 日志信息
        cpp_version_log,
        python_version_log,
        
        # 节点
        cpp_navigator_node,
        python_navigator_node,
        map_server_node,
        lifecycle_manager_node,
    ])