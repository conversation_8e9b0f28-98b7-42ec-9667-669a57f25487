#!/usr/bin/env python3

import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, GroupAction, TimerAction
from launch.conditions import IfCondition
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node, PushRosNamespace


def generate_launch_description():
    """Generate launch description for differential tracked navigator"""
    
    # Declare launch arguments
    use_sim_time_arg = DeclareLaunchArgument(
        'use_sim_time',
        default_value='false',
        description='Use simulation time if true'
    )
    
    map_file_arg = DeclareLaunchArgument(
        'map_file',
        default_value=os.path.join(
            get_package_share_directory('differential_tracked'), 'maps', 'map.yaml'
        ),
        description='Path to map file (*.pgm with *.yaml metadata)'
    )
    
    namespace_arg = DeclareLaunchArgument(
        'namespace',
        default_value='',
        description='Robot namespace'
    )
    
    waypoints_file_arg = DeclareLaunchArgument(
        'waypoints_file',
        default_value='point.txt',
        description='Path to waypoints file'
    )
    
    config_file_arg = DeclareLaunchArgument(
        'config_file',
        default_value=os.path.join(
            get_package_share_directory('differential_tracked'), 'config', 'navigator_params.yaml'
        ),
        description='Path to configuration file'
    )
    
    rviz_config_arg = DeclareLaunchArgument(
        'rviz_config',
        default_value=os.path.join(
            get_package_share_directory('differential_tracked'), 'rviz', 'simple.rviz'
        ),
        description='Path to RViz configuration file'
    )
    
    use_rviz_arg = DeclareLaunchArgument(
        'use_rviz',
        default_value='true',
        description='Launch RViz for visualization'
    )
    
    # Get launch configurations
    use_sim_time = LaunchConfiguration('use_sim_time')
    map_file = LaunchConfiguration('map_file')
    namespace = LaunchConfiguration('namespace')
    waypoints_file = LaunchConfiguration('waypoints_file')
    config_file = LaunchConfiguration('config_file')
    rviz_config = LaunchConfiguration('rviz_config')
    use_rviz = LaunchConfiguration('use_rviz')
    
    # Map server node (using reference implementation)
    start_map_server = Node(
        package='nav2_map_server',
        executable='map_server',
        name='map_server',
        output='screen',
        parameters=[{
            'yaml_filename': map_file,
            'use_sim_time': use_sim_time
        }]
    )
    
    # Lifecycle manager to manage map server (using reference implementation)
    start_lifecycle_manager = Node(
        package='nav2_lifecycle_manager',
        executable='lifecycle_manager',
        name='lifecycle_manager_map',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
            'autostart': True,
            'node_names': ['map_server']
        }]
    )
    
    # Navigation group with namespace
    navigation_group = GroupAction([
        PushRosNamespace(namespace),
        
        
        # Global trajectory generator node
        Node(
            package='global_traj_generate',
            executable='global_traj_generate_ros2',
            name='global_traj_generate',
            output='screen',
            parameters=[
                {
                    'use_sim_time': use_sim_time,
                    'waypoints_file': waypoints_file,
                    'goal_tolerance': 0.5,
                    'trajectory_update_rate': 10.0,
                }
            ],
            remappings=[
                ('/state_estimation', '/state_estimation'),
                ('/path', '/path'),
                ('/local_goal', '/local_goal'),
                ('/stopAtTarget', '/stopAtTarget'),
                ('/stop_navigation', '/stop_navigation'),
            ]
        ),
        
        # Differential tracked navigator node
        Node(
            package='differential_tracked',
            executable='differential_tracked_navigator',
            name='differential_tracked_navigator',
            output='screen',
            parameters=[
                config_file,
                {
                    'use_sim_time': use_sim_time,
                }
            ],
            remappings=[
                ('/state_estimation', '/state_estimation'),
                ('/registered_scan', '/registered_scan'),
                ('/map', '/map'),
                ('/cmd_vel', '/cmd_vel'),
                ('/way_point', '/way_point'),
                ('/local_goal', '/local_goal'),
                ('/stop_navigation', '/stop_navigation'),
                ('/stopAtTarget', '/stopAtTarget'),
                ('/stop', '/stop'),
                ('/vehicle/lateral_deviation', '/vehicle/lateral_deviation'),
            ]
        ),
        
        # Point Publisher node
        Node(
            package='point_publisher',
            executable='point_publish',
            name='point_publish_node',
            output='screen',
            parameters=[
                {
                    'use_sim_time': use_sim_time,
                    'waypoints_file': waypoints_file,
                    'publish_rate': 1.0,
                    'auto_publish': False,
                }
            ],
            remappings=[
                ('/state_estimation', '/state_estimation'),
                ('/start_navigation', '/start_navigation'),
                ('/path', '/path'),
            ]
        ),
        
        # Static transform publishers
        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='map_to_odom',
            arguments=['--x', '0', '--y', '0', '--z', '0', 
                      '--yaw', '0', '--pitch', '0', '--roll', '0', 
                      '--frame-id', 'map', '--child-frame-id', 'odom'],
            parameters=[{'use_sim_time': use_sim_time}]
        ),
        
        Node(
            package='tf2_ros',
            executable='static_transform_publisher',
            name='odom_to_base_link',
            arguments=['--x', '0', '--y', '0', '--z', '0', 
                      '--yaw', '0', '--pitch', '0', '--roll', '0', 
                      '--frame-id', 'odom', '--child-frame-id', 'base_link'],
            parameters=[{'use_sim_time': use_sim_time}]
        ),
    ])
    
    # RViz node (delayed start like in reference)
    start_rviz = Node(
        package='rviz2',
        executable='rviz2',
        name='rviz2',
        arguments=['-d', rviz_config],
        parameters=[{'use_sim_time': use_sim_time}],
        condition=IfCondition(use_rviz),
        output='screen'
    )
    
    # Delayed RViz start to ensure other nodes are ready
    delayed_start_rviz = TimerAction(
        period=3.0,
        actions=[start_rviz]
    )
    
    # Create launch description
    ld = LaunchDescription()
    
    # Add arguments
    ld.add_action(use_sim_time_arg)
    ld.add_action(map_file_arg)
    ld.add_action(namespace_arg)
    ld.add_action(waypoints_file_arg)
    ld.add_action(config_file_arg)
    ld.add_action(rviz_config_arg)
    ld.add_action(use_rviz_arg)
    
    # Add nodes
    ld.add_action(start_map_server)
    ld.add_action(start_lifecycle_manager)
    ld.add_action(navigation_group)
    ld.add_action(delayed_start_rviz)
    
    return ld