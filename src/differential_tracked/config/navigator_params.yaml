differential_tracked_navigator:
  ros__parameters:
    # === Pure Pursuit算法专用参数 ===
    
    # 车辆物理参数 (实际使用的参数)
    vehicle.track_width: 1.6                  # 车辆履带间距，用于障碍物检测范围 (米)
    vehicle.max_linear_velocity: 1.5          # Pure Pursuit最大线速度 (m/s)
    vehicle.min_linear_velocity: 0.0          # Pure Pursuit最小线速度 (m/s)
    vehicle.max_angular_velocity: 1.6         # Pure Pursuit最大角速度 (rad/s)

    # Pure Pursuit控制参数
    control.goal_tolerance: 0.3               # 到达航点的距离阈值 (米)
    control.angle_deadzone_degrees: 1.0       # 角度控制死区 (度)
    control.linear_velocity_factor: 0.5       # 线速度计算系数 (distance * factor)
    
    # Pure Pursuit距离分层控制增益
    control.far_distance_threshold: 3.0       # 远距离阈值 (米)
    control.far_distance_angular_gain: 2.5    # 远距离角速度增益
    control.medium_distance_threshold: 1.5    # 中距离阈值 (米)  
    control.medium_distance_angular_gain: 2.0 # 中距离角速度增益
    control.close_distance_threshold: 0.8     # 近距离阈值 (米)
    control.close_distance_angular_gain: 1.5  # 中近距离角速度增益
    control.very_close_angular_gain: 1.0      # 很近距离角速度增益
    control.close_distance_factor: 0.8        # 中近距离距离系数
    control.min_distance_factor: 0.5          # 最小距离响应系数

    # 障碍物检测和安全参数 (实际使用的参数)
    safety.obstacle_detection_range: 3.0      # 障碍物检测范围 (米)
    safety.safety_stop_distance: 2.5          # 安全停车距离 (米)
    safety.emergency_stop_distance: 1.0       # 紧急停车距离 (米) - 修正为与代码一致
    safety.enable_obstacle_detection: false     # 启用障碍物检测 - 修正为与代码一致

    # 调试和监控参数
    debug.odometry_print_frequency: 20        # 每N次里程计回调打印一次位置信息
    debug.no_path_warning_frequency: 50       # 每N次控制循环打印无路径警告