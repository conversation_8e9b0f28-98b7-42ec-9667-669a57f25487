/**
 * @file differential_tracked_navigator.cpp
 * @brief ROS2差分履带车导航控制器 - 使用PathFollower Pure Pursuit算法
 * 
 * 集成pathFollower的控制逻辑：
 * 1. Pure Pursuit + Look-ahead算法
 * 2. 基于角度差的转向控制
 * 3. 渐进式速度控制
 * 4. 距离减速控制
 * 
 * <AUTHOR>
 * @version 4.0 (PathFollower集成版)
 */

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <geometry_msgs/msg/point_stamped.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/path.hpp>
#include <std_msgs/msg/empty.hpp>
#include <std_msgs/msg/int8.hpp>

#include <cmath>
#include <memory>

/**
 * @class PathFollowerNavigator
 * @brief 基于Pure Pursuit算法的差分履带车导航控制器
 */
class PathFollowerNavigator : public rclcpp::Node 
{
public:
    PathFollowerNavigator() : Node("differential_tracked_navigator") 
    {
        // 声明PathFollower风格的参数
        this->declare_parameter("control.look_ahead_distance", 0.5);
        this->declare_parameter("control.yaw_rate_gain", 1.0);
        this->declare_parameter("control.stop_yaw_rate_gain", 1.0);
        this->declare_parameter("control.max_yaw_rate", 30.0);  // degrees
        this->declare_parameter("control.max_speed", 0.8);
        this->declare_parameter("control.max_accel", 3.0);
        this->declare_parameter("control.dir_diff_threshold", 0.1);  // radians
        this->declare_parameter("control.stop_distance_threshold", 0.2);
        this->declare_parameter("control.slow_down_distance_threshold", 1.0);
        this->declare_parameter("control.goal_tolerance", 0.2);
        this->declare_parameter("control.control_frequency", 10.0);
        this->declare_parameter("control.no_rotation_at_goal", true);

        // 加载参数
        loadParameters();

        // 初始化控制变量
        current_waypoint_index_ = 0;
        is_navigating_ = false;
        navigation_stopped_ = false;
        vehicle_speed_ = 0.0;
        vehicle_yaw_rate_ = 0.0;
        path_point_id_ = 0;
        
        // 记录姿态信息（用于PathFollower算法）
        vehicle_x_rec_ = 0.0;
        vehicle_y_rec_ = 0.0;
        vehicle_yaw_rec_ = 0.0;

        // 设置发布者
        cmd_vel_publisher_ = this->create_publisher<geometry_msgs::msg::TwistStamped>("/cmd_vel", 10);
        waypoint_publisher_ = this->create_publisher<geometry_msgs::msg::PointStamped>("/way_point", 10);
        stop_at_target_publisher_ = this->create_publisher<std_msgs::msg::Empty>("/stopAtTarget", 10);

        // 设置订阅者
        state_estimation_subscriber_ = this->create_subscription<nav_msgs::msg::Odometry>(
            "/state_estimation", 10,
            std::bind(&PathFollowerNavigator::stateEstimationCallback, this, std::placeholders::_1));

        path_subscriber_ = this->create_subscription<nav_msgs::msg::Path>(
            "/path", 10,
            std::bind(&PathFollowerNavigator::pathCallback, this, std::placeholders::_1));

        stop_navigation_subscriber_ = this->create_subscription<std_msgs::msg::Int8>(
            "/stop_navigation", 10,
            std::bind(&PathFollowerNavigator::stopNavigationCallback, this, std::placeholders::_1));

        // 启动控制定时器
        double control_period = 1.0 / control_frequency_;
        control_timer_ = this->create_wall_timer(
            std::chrono::duration<double>(control_period),
            std::bind(&PathFollowerNavigator::controlLoop, this));

        RCLCPP_INFO(get_logger(), "基于PathFollower算法的差分履带车导航控制器初始化完成");
    }

private:
    void loadParameters() 
    {
        look_ahead_distance_ = this->get_parameter("control.look_ahead_distance").as_double();
        yaw_rate_gain_ = this->get_parameter("control.yaw_rate_gain").as_double();
        stop_yaw_rate_gain_ = this->get_parameter("control.stop_yaw_rate_gain").as_double();
        max_yaw_rate_ = this->get_parameter("control.max_yaw_rate").as_double();
        max_speed_ = this->get_parameter("control.max_speed").as_double();
        max_accel_ = this->get_parameter("control.max_accel").as_double();
        dir_diff_threshold_ = this->get_parameter("control.dir_diff_threshold").as_double();
        stop_distance_threshold_ = this->get_parameter("control.stop_distance_threshold").as_double();
        slow_down_distance_threshold_ = this->get_parameter("control.slow_down_distance_threshold").as_double();
        goal_tolerance_ = this->get_parameter("control.goal_tolerance").as_double();
        control_frequency_ = this->get_parameter("control.control_frequency").as_double();
        no_rotation_at_goal_ = this->get_parameter("control.no_rotation_at_goal").as_bool();
    }

    void pathCallback(const nav_msgs::msg::Path::SharedPtr msg) 
    {
        current_path_ = msg;
        
        if (!msg->poses.empty()) {
            current_waypoint_index_ = 0;
            current_target_pose_ = std::make_shared<geometry_msgs::msg::PoseStamped>(msg->poses[0]);
            
            if (!navigation_stopped_) {
                is_navigating_ = true;
                RCLCPP_INFO(get_logger(), "开始导航 - 路径包含 %zu 个航点", msg->poses.size());
                publishCurrentWaypoint();
                
                // 记录接收到轨迹时刻的载体位姿信息（PathFollower风格）
                if (current_pose_) {
                    vehicle_x_rec_ = current_pose_->position.x;
                    vehicle_y_rec_ = current_pose_->position.y;
                    vehicle_yaw_rec_ = getYawFromQuaternion(current_pose_->orientation);
                    path_point_id_ = 0;  // 重置路径点ID
                    
                    RCLCPP_INFO(get_logger(), "记录轨迹接收时刻位姿: (%.3f, %.3f, %.1f°)",
                               vehicle_x_rec_, vehicle_y_rec_, vehicle_yaw_rec_ * 180.0 / M_PI);
                }
            }
        } else {
            RCLCPP_WARN(get_logger(), "收到空路径");
            is_navigating_ = false;
        }
    }

    void stopNavigationCallback(const std_msgs::msg::Int8::SharedPtr msg) 
    {
        (void)msg;
        navigation_stopped_ = true;
        is_navigating_ = false;
        stopVehicle();
        RCLCPP_INFO(get_logger(), "收到停止导航信号");
    }

    void stateEstimationCallback(const nav_msgs::msg::Odometry::SharedPtr msg) 
    {
        current_pose_ = std::make_shared<geometry_msgs::msg::Pose>(msg->pose.pose);
        
        // 保存当前速度信息用于PathFollower算法
        float odo_vel_x = msg->twist.twist.linear.x;
        float odo_vel_y = msg->twist.twist.linear.y;
        float odo_vel_z = msg->twist.twist.linear.z;
        current_velocity_ = std::sqrt(odo_vel_x * odo_vel_x + odo_vel_y * odo_vel_y + odo_vel_z * odo_vel_z);
    }

    void publishCurrentWaypoint() 
    {
        if (current_target_pose_) {
            geometry_msgs::msg::PointStamped point_msg;
            point_msg.header.stamp = this->get_clock()->now();
            point_msg.header.frame_id = "map";
            point_msg.point = current_target_pose_->pose.position;
            waypoint_publisher_->publish(point_msg);
        }
    }

    void controlLoop() 
    {
        // 基本检查
        if (!is_navigating_ || !current_pose_ || !current_path_ || 
            !current_target_pose_ || navigation_stopped_) {
            return;
        }

        // 检查是否到达当前目标点
        if (reachedCurrentTarget()) {
            if (current_waypoint_index_ < current_path_->poses.size() - 1) {
                // 移动到下一个航点（PathFollower风格的切换逻辑）
                size_t prev_index = current_waypoint_index_;
                current_waypoint_index_++;
                current_target_pose_ = std::make_shared<geometry_msgs::msg::PoseStamped>(
                    current_path_->poses[current_waypoint_index_]);
                
                RCLCPP_INFO(get_logger(), "[路径点切换] 从路径点%zu切换到%zu, 新目标: (%.2f, %.2f)",
                          prev_index, current_waypoint_index_,
                          current_target_pose_->pose.position.x,
                          current_target_pose_->pose.position.y);
                
                publishCurrentWaypoint();
                
                // PathFollower风格：重新记录位姿信息和重置路径点ID
                if (current_pose_) {
                    vehicle_x_rec_ = current_pose_->position.x;
                    vehicle_y_rec_ = current_pose_->position.y;
                    vehicle_yaw_rec_ = getYawFromQuaternion(current_pose_->orientation);
                    path_point_id_ = 0;  // 重置路径点ID
                    
                    RCLCPP_INFO(get_logger(), "[路径点切换后位姿更新] (%.3f, %.3f, %.1f°)",
                               vehicle_x_rec_, vehicle_y_rec_, vehicle_yaw_rec_ * 180.0 / M_PI);
                }
            } else {
                // 到达最终目标
                stopVehicle();
                is_navigating_ = false;
                
                std_msgs::msg::Empty stop_msg;
                stop_at_target_publisher_->publish(stop_msg);
                
                RCLCPP_INFO(get_logger(), "到达最终目标点 - 导航完成");
                return;
            }
        }

        // 计算并发布控制命令
        auto cmd_vel = calculateControlCommand();
        cmd_vel_publisher_->publish(cmd_vel);
    }

    struct RelativePosition {
        double x;
        double y;
        double distance;
        double angle;
    };

    RelativePosition getRelativePosition() 
    {
        RelativePosition result = {0.0, 0.0, 0.0, 0.0};
        
        if (!current_target_pose_ || !current_pose_) {
            return result;
        }

        // 获取车辆当前位置和朝向（map坐标系）
        double vehicleXRec = current_pose_->position.x;
        double vehicleYRec = current_pose_->position.y;
        double vehicleYawRec = getYawFromQuaternion(current_pose_->orientation);

        // 获取目标航点位置（map坐标系）
        double vehicleX = current_target_pose_->pose.position.x;
        double vehicleY = current_target_pose_->pose.position.y;

        // 将目标航点从map坐标系转换到车体坐标系
        result.x = cos(vehicleYawRec) * (vehicleX - vehicleXRec) 
                 + sin(vehicleYawRec) * (vehicleY - vehicleYRec);
        result.y = -sin(vehicleYawRec) * (vehicleX - vehicleXRec) 
                 + cos(vehicleYawRec) * (vehicleY - vehicleYRec);

        // 计算距离和角度
        result.distance = sqrt(result.x * result.x + result.y * result.y);
        result.angle = atan2(result.y, result.x);
        
        return result;
    }

    bool reachedCurrentTarget() 
    {
        if (!current_target_pose_ || !current_pose_ || !current_path_) {
            return false;
        }
        
        // *** 使用PathFollower风格的到达判断 ***
        // 与PathFollower.cpp中的逻辑保持一致
        
        // 计算到目标点的距离
        double target_x = current_target_pose_->pose.position.x;
        double target_y = current_target_pose_->pose.position.y;
        double current_x = current_pose_->position.x;
        double current_y = current_pose_->position.y;
        double distance = std::sqrt((target_x - current_x) * (target_x - current_x) + 
                                   (target_y - current_y) * (target_y - current_y));
        
        // PathFollower风格：只用距离判断，不用角度判断
        bool reached = distance < goal_tolerance_;
        
        // 调试信息：简化版本
        if (distance < goal_tolerance_ * 2.0) {  // 在2倍容差范围内
            RCLCPP_INFO(get_logger(), "[路径点到达检查-PathFollower风格] 距离=%.3fm, 阈值=%.3fm, 状态=%s",
                       distance, goal_tolerance_, reached ? "已到达" : "未到达");
        }
        
        return reached;
    }

    geometry_msgs::msg::TwistStamped calculateControlCommand() 
    {
        geometry_msgs::msg::TwistStamped cmd_msg;
        cmd_msg.header.stamp = this->get_clock()->now();
        cmd_msg.header.frame_id = "base_link";

        if (!current_target_pose_ || !current_pose_ || !current_path_) {
            return cmd_msg; // 返回零命令
        }

        // *** PathFollower Pure Pursuit 算法 ***
        // 计算接收到轨迹时的位置在当前载体系下的坐标
        double current_x = current_pose_->position.x;
        double current_y = current_pose_->position.y;
        double current_yaw = getYawFromQuaternion(current_pose_->orientation);
        
        double vehicle_x_rel = std::cos(vehicle_yaw_rec_) * (current_x - vehicle_x_rec_) + 
                              std::sin(vehicle_yaw_rec_) * (current_y - vehicle_y_rec_);
        double vehicle_y_rel = -std::sin(vehicle_yaw_rec_) * (current_x - vehicle_x_rec_) + 
                              std::cos(vehicle_yaw_rec_) * (current_y - vehicle_y_rec_);

        // 计算接收到轨迹时的位置距轨迹最后一个点的距离
        int path_size = current_path_->poses.size();
        double end_dis_x = current_path_->poses[path_size - 1].pose.position.x - vehicle_x_rel;
        double end_dis_y = current_path_->poses[path_size - 1].pose.position.y - vehicle_y_rel;
        double end_dis = std::sqrt(end_dis_x * end_dis_x + end_dis_y * end_dis_y);

        // 寻找前视距离范围内的最后一个点
        double dis_x, dis_y, dis;
        while (path_point_id_ < path_size - 1) {
            dis_x = current_path_->poses[path_point_id_].pose.position.x - vehicle_x_rel;
            dis_y = current_path_->poses[path_point_id_].pose.position.y - vehicle_y_rel;
            dis = std::sqrt(dis_x * dis_x + dis_y * dis_y);
            if (dis < look_ahead_distance_) {
                path_point_id_++;
            } else {
                break;
            }
        }

        // 计算接收到轨迹时的位置距前视距离内最后一个点的距离以及角度
        dis_x = current_path_->poses[path_point_id_].pose.position.x - vehicle_x_rel;
        dis_y = current_path_->poses[path_point_id_].pose.position.y - vehicle_y_rel;
        dis = std::sqrt(dis_x * dis_x + dis_y * dis_y);
        double path_dir = std::atan2(dis_y, dis_x);

        // 计算当前位置与前视距离最后一个点的夹角
        double dir_diff = current_yaw - vehicle_yaw_rec_ - path_dir;
        
        // 保证夹角在-pi到pi内
        dir_diff = normalizeAngle(dir_diff);

        // 计算载体距离终点距离
        double distance_to_goal = std::sqrt(
            std::pow(current_x - current_target_pose_->pose.position.x, 2) + 
            std::pow(current_y - current_target_pose_->pose.position.y, 2)
        );

        // 计算载体在3米内减速到最小速度所需的加速度
        double decel_distance = 3.0;
        double min_speed = 0.1;
        double a = (max_speed_ * max_speed_ - min_speed * min_speed) / (2 * decel_distance);
        
        // 判断是否进入减速范围
        double target_speed;
        if (distance_to_goal < decel_distance) {
            target_speed = std::sqrt(2 * a * distance_to_goal + min_speed * min_speed);
        } else {
            target_speed = max_speed_;
        }

        // 计算控制输出
        // 判断线速度是否为0，若为0则采用停止转角增益，若不为0则采用普通转角增益
        if (std::abs(vehicle_speed_) < 2.0 * max_accel_ / 100.0) {
            vehicle_yaw_rate_ = -stop_yaw_rate_gain_ * dir_diff;
        } else {
            vehicle_yaw_rate_ = -yaw_rate_gain_ * dir_diff;
        }

        // 对载体角速度进行限幅
        double max_yaw_rate_rad = max_yaw_rate_ * M_PI / 180.0;
        if (vehicle_yaw_rate_ > max_yaw_rate_rad) {
            vehicle_yaw_rate_ = max_yaw_rate_rad;
        } else if (vehicle_yaw_rate_ < -max_yaw_rate_rad) {
            vehicle_yaw_rate_ = -max_yaw_rate_rad;
        }

        // 若局部距离小于阈值角速度为0
        if (path_size <= 1 || (dis < stop_distance_threshold_ && no_rotation_at_goal_)) {
            vehicle_yaw_rate_ = 0;
        }

        // 若局部距离小于减速距离则进行减速
        double joy_speed = target_speed;
        if (path_size <= 1) {
            joy_speed = 0;
        } else if (end_dis < slow_down_distance_threshold_) {
            joy_speed *= end_dis / slow_down_distance_threshold_;
        }

        // 若夹角小于阈值且在停止范围外则正常加减速
        if (std::abs(dir_diff) < dir_diff_threshold_ && dis > stop_distance_threshold_) {
            if (current_velocity_ < 0.20) {
                vehicle_speed_ = 0.40;  // 起步速度
            } else if (vehicle_speed_ < joy_speed) {
                vehicle_speed_ += max_accel_ / 100.0;
            } else if (vehicle_speed_ > joy_speed) {
                vehicle_speed_ -= max_accel_ / 100.0;
            }
        } else {
            // 若夹角大于阈值，则先考虑转弯再前进
            if (vehicle_speed_ > 0) {
                vehicle_speed_ -= max_accel_ / 100.0;
            } else if (vehicle_speed_ < 0) {
                vehicle_speed_ += max_accel_ / 100.0;
            }
        }

        // 设置最终输出
        if (std::abs(vehicle_speed_) <= max_accel_ / 100.0) {
            cmd_msg.twist.linear.x = 0.3;
        } else {
            cmd_msg.twist.linear.x = vehicle_speed_;
        }

        if (std::abs(vehicle_yaw_rate_) <= 0.01) {
            cmd_msg.twist.angular.z = 0;
        } else {
            cmd_msg.twist.angular.z = vehicle_yaw_rate_;
        }

        // 调试信息：PathFollower控制算法识别信息
        RCLCPP_INFO(get_logger(), "[PathFollower-C++] 前视距离=%.3fm, 路径点ID=%d, 角度差=%.1f°, 目标速度=%.3f",
                    look_ahead_distance_, path_point_id_, dir_diff * 180.0 / M_PI, target_speed);
                    
        // 调试信息：详细的PathFollower控制分析
        RCLCPP_DEBUG(get_logger(), "[PathFollower控制] 距离=%.3fm, 角度差=%.1f°, 目标速度=%.3f, "
                    "线速度=%.3f, 角速度=%.3f, 路径点ID=%d",
                    dis, dir_diff * 180.0 / M_PI, target_speed,
                    cmd_msg.twist.linear.x, cmd_msg.twist.angular.z, path_point_id_);

        // PathFollower风格警告：低速度检测
        if (std::abs(cmd_msg.twist.linear.x) < 0.05) {
            RCLCPP_WARN(get_logger(), "[PathFollower-C++] 检测到低/零线速度: "
                       "距离=%.2fm, 角度差=%.1f°, 线速度=%.3f, 角速度=%.3f, "
                       "对齐状态=%s, 路径点ID=%d",
                       dis, dir_diff * 180.0 / M_PI,
                       cmd_msg.twist.linear.x, cmd_msg.twist.angular.z,
                       (std::abs(dir_diff) < dir_diff_threshold_) ? "已对齐" : "未对齐",
                       path_point_id_);
        }

        return cmd_msg;
    }

    void stopVehicle() 
    {
        geometry_msgs::msg::TwistStamped stop_msg;
        stop_msg.header.stamp = this->get_clock()->now();
        stop_msg.header.frame_id = "base_link";
        cmd_vel_publisher_->publish(stop_msg);
    }

    double getYawFromQuaternion(const geometry_msgs::msg::Quaternion& quat) 
    {
        // 四元数到欧拉角转换（只计算yaw）
        double siny_cosp = 2 * (quat.w * quat.z + quat.x * quat.y);
        double cosy_cosp = 1 - 2 * (quat.y * quat.y + quat.z * quat.z);
        return std::atan2(siny_cosp, cosy_cosp);
    }

    double normalizeAngle(double angle) 
    {
        // 使用atan2方法确保角度在[-π, π]范围内，更稳定和高效
        // 这是解决PID跟踪问题的关键函数
        return std::atan2(std::sin(angle), std::cos(angle));
    }

    // PathFollower风格的参数变量
    double look_ahead_distance_;
    double yaw_rate_gain_;
    double stop_yaw_rate_gain_;
    double max_yaw_rate_;  // degrees
    double max_speed_;
    double max_accel_;
    double dir_diff_threshold_;  // radians
    double stop_distance_threshold_;
    double slow_down_distance_threshold_;
    double goal_tolerance_;
    double control_frequency_;
    bool no_rotation_at_goal_;

    // PathFollower控制状态变量
    double vehicle_speed_;
    double vehicle_yaw_rate_;
    double current_velocity_;  // 从里程计获取的当前速度
    int path_point_id_;        // 当前路径点ID
    
    // 记录姿态信息（用于PathFollower算法）
    double vehicle_x_rec_;
    double vehicle_y_rec_;
    double vehicle_yaw_rec_;
    
    // ROS2接口
    rclcpp::Publisher<geometry_msgs::msg::TwistStamped>::SharedPtr cmd_vel_publisher_;
    rclcpp::Publisher<geometry_msgs::msg::PointStamped>::SharedPtr waypoint_publisher_;
    rclcpp::Publisher<std_msgs::msg::Empty>::SharedPtr stop_at_target_publisher_;
    
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr state_estimation_subscriber_;
    rclcpp::Subscription<nav_msgs::msg::Path>::SharedPtr path_subscriber_;
    rclcpp::Subscription<std_msgs::msg::Int8>::SharedPtr stop_navigation_subscriber_;
    
    rclcpp::TimerBase::SharedPtr control_timer_;
    
    // 状态变量
    std::shared_ptr<geometry_msgs::msg::Pose> current_pose_;
    std::shared_ptr<nav_msgs::msg::Path> current_path_;
    std::shared_ptr<geometry_msgs::msg::PoseStamped> current_target_pose_;
    size_t current_waypoint_index_;
    bool is_navigating_;
    bool navigation_stopped_;
};

int main(int argc, char **argv) 
{
    rclcpp::init(argc, argv);
    
    try {
        auto navigator = std::make_shared<PathFollowerNavigator>();
        rclcpp::spin(navigator);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(rclcpp::get_logger("main"), "Error: %s", e.what());
        return 1;
    }
    
    rclcpp::shutdown();
    return 0;
}