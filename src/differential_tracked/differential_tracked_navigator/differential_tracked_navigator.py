#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist, TwistStamped, PointStamped, PoseStamped
from nav_msgs.msg import Odometry
from sensor_msgs.msg import PointCloud2
from std_msgs.msg import Empty, Int8
from rclpy.qos import QoSProfile, DurabilityPolicy, ReliabilityPolicy
import sensor_msgs_py.point_cloud2 as pc2
import numpy as np
import math

class DifferentialTrackedNavigator(Node):
    def __init__(self):
        super().__init__('differential_tracked_navigator')
        self.get_logger().info('Initializing Differential Tracked Navigator Node')
        
        # 声明ROS2参数
        self._declare_parameters()
        
        # 从参数服务器加载参数
        self._load_parameters()
        
        # Robot state
        self.robot_pose = np.zeros(3)  # [x, y, theta]
        self.current_goal = None       # 从/local_goal获取的当前目标点
        self.goal_received = False
        
        # 障碍物检测状态
        self.obstacle_detected = False      # 障碍物检测标志
        self.emergency_stop = False         # 紧急停车标志
        
        # 导航控制状态
        self.navigation_stopped = False     # 外部停止导航标志

        # Subscribers - 适配当前系统的topics
        self.create_subscription(Odometry, '/state_estimation', self.odom_callback, 10)
        self.create_subscription(PointCloud2, '/registered_scan', self.lidar_callback, 10)
        self.create_subscription(PoseStamped, '/local_goal', self.goal_callback, 10)
        self.create_subscription(Int8, '/stop_navigation', self.stop_navigation_callback, 10)

        # Publishers - 适配当前系统的topics
        self.cmd_vel_publisher = self.create_publisher(Twist, '/cmd_vel', 10)
        self.waypoint_publisher = self.create_publisher(PointStamped, '/way_point', 10)
        
        # 障碍物相关发布者
        self.stop_publisher = self.create_publisher(Int8, '/stop', 10)

        self.get_logger().info('Subscriptions and Publishers initialized')
        self._print_parameter_summary()
    
    def _declare_parameters(self):
        """声明ROS2参数"""
        # 车辆物理参数
        self.declare_parameter('vehicle.track_width', 2.0)
        self.declare_parameter('vehicle.max_linear_velocity', 1.0)
        self.declare_parameter('vehicle.min_linear_velocity', 0.1)
        self.declare_parameter('vehicle.max_angular_velocity', 1.4)
        
        # Pure Pursuit控制参数
        self.declare_parameter('control.goal_tolerance', 0.3)
        self.declare_parameter('control.angle_deadzone_degrees', 2.0)
        self.declare_parameter('control.linear_velocity_factor', 0.6)
        
        # Pure Pursuit距离分层控制增益
        self.declare_parameter('control.far_distance_threshold', 3.0)
        self.declare_parameter('control.far_distance_angular_gain', 2.5)
        self.declare_parameter('control.medium_distance_threshold', 1.5)
        self.declare_parameter('control.medium_distance_angular_gain', 2.0)
        self.declare_parameter('control.close_distance_threshold', 0.8)
        self.declare_parameter('control.close_distance_angular_gain', 1.5)
        self.declare_parameter('control.very_close_angular_gain', 1.0)
        self.declare_parameter('control.close_distance_factor', 0.8)
        self.declare_parameter('control.min_distance_factor', 0.5)
        
        # 障碍物检测和安全参数
        self.declare_parameter('safety.obstacle_detection_range', 2.0)
        self.declare_parameter('safety.safety_stop_distance', 0.8)
        self.declare_parameter('safety.emergency_stop_distance', 0.4)
        self.declare_parameter('safety.enable_obstacle_detection', True)
        
        # 调试和监控参数
        self.declare_parameter('debug.odometry_print_frequency', 20)
        self.declare_parameter('debug.no_goal_warning_frequency', 50)
    
    def _load_parameters(self):
        """从参数服务器加载参数"""
        # 车辆物理参数
        self.track_width = self.get_parameter('vehicle.track_width').value
        self.max_linear_velocity = self.get_parameter('vehicle.max_linear_velocity').value
        self.min_linear_velocity = self.get_parameter('vehicle.min_linear_velocity').value
        self.max_angular_velocity = self.get_parameter('vehicle.max_angular_velocity').value
        
        # Pure Pursuit控制参数
        self.goal_tolerance = self.get_parameter('control.goal_tolerance').value
        self.angle_deadzone_degrees = self.get_parameter('control.angle_deadzone_degrees').value
        self.linear_velocity_factor = self.get_parameter('control.linear_velocity_factor').value
        
        # Pure Pursuit距离分层控制增益
        self.far_distance_threshold = self.get_parameter('control.far_distance_threshold').value
        self.far_distance_angular_gain = self.get_parameter('control.far_distance_angular_gain').value
        self.medium_distance_threshold = self.get_parameter('control.medium_distance_threshold').value
        self.medium_distance_angular_gain = self.get_parameter('control.medium_distance_angular_gain').value
        self.close_distance_threshold = self.get_parameter('control.close_distance_threshold').value
        self.close_distance_angular_gain = self.get_parameter('control.close_distance_angular_gain').value
        self.very_close_angular_gain = self.get_parameter('control.very_close_angular_gain').value
        self.close_distance_factor = self.get_parameter('control.close_distance_factor').value
        self.min_distance_factor = self.get_parameter('control.min_distance_factor').value
        
        # 障碍物检测和安全参数
        self.obstacle_detection_range = self.get_parameter('safety.obstacle_detection_range').value
        self.safety_stop_distance = self.get_parameter('safety.safety_stop_distance').value
        self.emergency_stop_distance = self.get_parameter('safety.emergency_stop_distance').value
        self.enable_obstacle_detection = self.get_parameter('safety.enable_obstacle_detection').value
        
        # 调试和监控参数
        self.odometry_print_frequency = self.get_parameter('debug.odometry_print_frequency').value
        self.no_goal_warning_frequency = self.get_parameter('debug.no_goal_warning_frequency').value
        
        # 转换角度参数为弧度
        self.angle_deadzone = math.radians(self.angle_deadzone_degrees)
    
    def _print_parameter_summary(self):
        """打印参数配置摘要"""
        self.get_logger().info('=== Differential Tracked Navigator参数配置 ===')
        self.get_logger().info(f'车辆参数: 履带宽度={self.track_width}m, 最大线速度={self.max_linear_velocity}m/s, 最小线速度={self.min_linear_velocity}m/s, 最大角速度={self.max_angular_velocity}rad/s')
        self.get_logger().info(f'控制参数: 到达容差={self.goal_tolerance}m, 角度死区={self.angle_deadzone_degrees}°, 线速度系数={self.linear_velocity_factor}')
        self.get_logger().info(f'安全参数: 检测范围={self.obstacle_detection_range}m, 障碍物检测={self.enable_obstacle_detection}')
        self.get_logger().info('==============================================')

    def goal_callback(self, msg: PoseStamped):
        """目标点消息回调函数"""
        # 提取目标点坐标
        goal_x = msg.pose.position.x
        goal_y = msg.pose.position.y
        
        # 更新当前目标点
        self.current_goal = np.array([goal_x, goal_y])
        self.goal_received = True
        
        # 详细的目标信息打印
        self.get_logger().info(f'=== NEW GOAL RECEIVED ===')
        self.get_logger().info(f'Goal position: [{goal_x:.3f}, {goal_y:.3f}]')
        self.get_logger().info(f'===========================')

    def stop_navigation_callback(self, msg: Int8):
        """停止导航消息回调函数"""
        if msg.data != 0:  # 非零值表示停止导航
            self.navigation_stopped = True
            self.goal_received = False  # 清除目标状态
            self.current_goal = None
            
            # 立即停止机器人
            self.stop_robot()
            
            self.get_logger().info('🛑 收到停止导航指令，机器人已停止')
        else:  # 零值表示恢复导航
            self.navigation_stopped = False
            self.get_logger().info('✅ 收到恢复导航指令，等待新目标')

    def odom_callback(self, msg: Odometry):
        """里程计回调函数"""
        position = msg.pose.pose.position
        orientation = msg.pose.pose.orientation
        
        # 更新机器人位置
        prev_pose = self.robot_pose.copy()
        self.robot_pose = np.array([
            position.x,
            position.y,
            self.quaternion_to_yaw(orientation)
        ])
        
        # 定期打印当前位置（每隔几次回调打印一次避免信息过多）
        if not hasattr(self, '_odom_counter'):
            self._odom_counter = 0
        self._odom_counter += 1
        
        if self._odom_counter % self.odometry_print_frequency == 0:  # 根据参数设置的频率打印
            self.get_logger().info(
                f'Robot pose: x={self.robot_pose[0]:.3f}, y={self.robot_pose[1]:.3f}, '
                f'theta={math.degrees(self.robot_pose[2]):.1f}°'
            )
        
        # 执行Pure Pursuit控制
        self.goal_pursuit_control()

    def lidar_callback(self, msg: PointCloud2):
        """
        激光雷达数据回调函数（参考1234版本实现）
        ===========================================
        
        处理激光雷达点云数据，进行实时障碍物检测。
        根据障碍物距离设置不同级别的安全响应。
        
        参数:
            msg: 激光雷达点云消息
        """
        # 如果障碍物检测被禁用，跳过处理
        if not self.enable_obstacle_detection:
            self.obstacle_detected = False
            self.emergency_stop = False
            return
            
        try:
            # 重置障碍物检测标志
            self.obstacle_detected = False
            self.emergency_stop = False
            
            # 将PointCloud2转换为点列表
            points = list(pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True))
            
            # 在安全区域内检查障碍物
            closest_obstacle_distance = float('inf')
            obstacle_count = 0
            closest_point = None  # 保存最近障碍物的坐标

            for point in points:
                x, y, z = point  # 2D导航忽略z坐标
                distance = math.sqrt(x**2 + y**2)  # 计算到车辆的距离

                # 检查点是否在检测范围内且在车辆前方
                if (distance < self.obstacle_detection_range and
                    x > 0 and  # 只考虑前方的点
                    abs(y) < self.track_width / 2 + 0.2):  # 在车辆宽度加余量范围内

                    obstacle_count += 1

                    # 更新最近障碍物信息
                    if distance < closest_obstacle_distance:
                        closest_obstacle_distance = distance
                        closest_point = (x, y, z)

                    # 根据距离设置不同的安全响应级别
                    if distance < self.emergency_stop_distance:
                        self.emergency_stop = True  # 紧急停车
                        self.get_logger().warn(f'紧急停车！障碍物距离: {distance:.2f}m')
                        # 打印激光点云x,y,z值
                        self.get_logger().warn(f'紧急停车点云数据: x={x:.3f}, y={y:.3f}, z={z:.3f}')
                        break
                    elif distance < self.safety_stop_distance:
                        self.obstacle_detected = True  # 安全停车
            
            # 调试信息
            if obstacle_count > 0:
                self.get_logger().info(
                    f'检测到 {obstacle_count} 个障碍物，最近距离: {closest_obstacle_distance:.2f}m'
                )
                # 打印最近障碍物的点云数据（需要在循环中保存最近点的坐标）
                
        except Exception as e:
            self.get_logger().warn(f'处理激光雷达数据时出错: {e}')

    def goal_pursuit_control(self):
        """Pure Pursuit算法控制逻辑（单目标点模式，集成障碍物检测和导航停止）"""
        # === 检查外部停止导航指令（最高优先级） ===
        if self.navigation_stopped:
            self.stop_robot()
            return
            
        if not self.goal_received:
            if not hasattr(self, '_no_goal_counter'):
                self._no_goal_counter = 0
            self._no_goal_counter += 1
            if self._no_goal_counter % self.no_goal_warning_frequency == 0:  # 根据参数设置的频率警告
                self.get_logger().warn('No goal received yet, waiting for /local_goal topic')
            return
            
        if self.current_goal is None:
            self.get_logger().warn('No valid goal available')
            return
        
        # === 障碍物安全检查（次高优先级） ===
        if self.enable_obstacle_detection:
            if self.emergency_stop:
                self.get_logger().warn('🚨 紧急停车！前方有障碍物过近')
                self.stop_robot()
                # 发布障碍物停车信号
                stop_msg = Int8()
                stop_msg.data = 1  # 1表示停车状态
                self.stop_publisher.publish(stop_msg)
                return
            elif self.obstacle_detected:
                self.get_logger().warn('⚠️ 检测到障碍物，安全停车')
                self.stop_robot()
                # 发布障碍物停车信号
                stop_msg = Int8()
                stop_msg.data = 1  # 1表示停车状态
                self.stop_publisher.publish(stop_msg)
                return
        
        # 检查是否到达目标点
        distance_to_goal = np.linalg.norm(self.robot_pose[:2] - self.current_goal)
        
        # 打印当前状态信息（包含障碍物状态和导航状态）
        self.get_logger().info(
            f'--- GOAL STATUS ---\n'
            f'Current goal: [{self.current_goal[0]:.3f}, {self.current_goal[1]:.3f}]\n'
            f'Distance to goal: {distance_to_goal:.3f}m\n'
            f'Navigation stopped: {self.navigation_stopped}\n'
            f'Obstacle status: Emergency={self.emergency_stop}, Detected={self.obstacle_detected}'
        )
        
        # 如果到达目标点，停止机器人
        if distance_to_goal < self.goal_tolerance:  # 到达阈值
            self.get_logger().info('🎯 GOAL REACHED! Stopping robot.')
            self.stop_robot()
            return
        
        # 发布当前目标点
        self.publish_current_waypoint(self.current_goal)
        
        # 计算Pure Pursuit控制
        control = self.calculate_pure_pursuit(self.current_goal)
        if control is not None:
            self.publish_velocity(control)

    def calculate_pure_pursuit(self, target):
        """计算Pure Pursuit控制输出（优化版）"""
        # 计算到目标点的距离和角度
        dx = target[0] - self.robot_pose[0]
        dy = target[1] - self.robot_pose[1]
        distance = math.sqrt(dx*dx + dy*dy)
        
        # 目标角度
        target_angle = math.atan2(dy, dx)
        
        # 角度误差
        angle_error = target_angle - self.robot_pose[2]
        # 归一化角度到[-pi, pi]
        angle_error = math.atan2(math.sin(angle_error), math.cos(angle_error))
        
        # 计算线速度（距离越远速度越大）
        linear_vel = min(self.max_linear_velocity, distance * self.linear_velocity_factor)
        # 应用最小线速度约束（只在前进时）
        if linear_vel > 0.0 and linear_vel < self.min_linear_velocity:
            linear_vel = self.min_linear_velocity
        
        # 改进的角速度控制 - 使用参数化的分层控制
        if abs(angle_error) < self.angle_deadzone:
            angular_vel = 0.0
            control_type = "deadzone"
        else:
            # 改进的距离调节策略 - 根据参数化的距离分层控制
            if distance > self.far_distance_threshold:
                # 远距离：最高响应性，不限制角速度
                distance_factor = 1.0
                angular_gain = self.far_distance_angular_gain
                control_type = "far_distance"
            elif distance > self.medium_distance_threshold:
                # 中距离：高响应性
                distance_factor = 1.0
                angular_gain = self.medium_distance_angular_gain
                control_type = "medium_distance" 
            elif distance > self.close_distance_threshold:
                # 中近距离：平衡响应性与稳定性
                distance_factor = self.close_distance_factor
                angular_gain = self.close_distance_angular_gain
                control_type = "medium_close"
            else:
                # 近距离：优先稳定性，但保持基本响应性
                distance_factor = max(self.min_distance_factor, distance / self.close_distance_threshold)
                angular_gain = self.very_close_angular_gain
                control_type = f"close_distance (factor: {distance_factor:.2f})"
            
            # 角度比例控制
            angular_vel = angle_error * angular_gain * distance_factor
            
            # 限制最大角速度
            angular_vel = max(-self.max_angular_velocity, min(self.max_angular_velocity, angular_vel))
            control_type += f" (gain: {angular_gain}, factor: {distance_factor:.2f})"
        
        # 详细的控制信息打印
        self.get_logger().info(
            f'--- CONTROL CALCULATION ---\n'
            f'Target angle: {math.degrees(target_angle):.1f}°\n'
            f'Current heading: {math.degrees(self.robot_pose[2]):.1f}°\n'
            f'Angle error: {math.degrees(angle_error):.1f}°\n'
            f'Distance: {distance:.3f}m\n'
            f'Control type: {control_type}\n'
            f'Control output: linear_vel={linear_vel:.3f} m/s, angular_vel={angular_vel:.3f} rad/s\n'
            f'---------------------------'
        )
        
        return [linear_vel, angular_vel]

    def publish_current_waypoint(self, target):
        """发布当前目标航点"""
        waypoint_msg = PointStamped()
        waypoint_msg.header.stamp = self.get_clock().now().to_msg()
        waypoint_msg.header.frame_id = 'map'
        waypoint_msg.point.x = float(target[0])
        waypoint_msg.point.y = float(target[1])
        waypoint_msg.point.z = 0.0
        self.waypoint_publisher.publish(waypoint_msg)

    def stop_robot(self):
        """停止机器人 - 接收/stop_navigation时输出线速度0.0"""
        twist = Twist()
        twist.linear.x = 0.0  # 接收/stop_navigation时，线速度必须设为0.0
        twist.angular.z = 0.0
        self.cmd_vel_publisher.publish(twist)

    def publish_velocity(self, control):
        """发布速度命令 - 应用最小线速度约束"""
        twist = Twist()
        linear_vel = float(control[0])
        # 应用最小线速度约束（只在前进时）
        if linear_vel > 0.0 and linear_vel < self.min_linear_velocity:
            linear_vel = self.min_linear_velocity
        twist.linear.x = linear_vel
        twist.angular.z = float(control[1])
        self.cmd_vel_publisher.publish(twist)

    def quaternion_to_yaw(self, q):
        """Convert quaternion to yaw angle."""
        return np.arctan2(2.0 * (q.w * q.z + q.x * q.y), 1.0 - 2.0 * (q.y ** 2 + q.z ** 2))


def main(args=None):
    rclpy.init(args=args)
    navigator = DifferentialTrackedNavigator()
    rclpy.spin(navigator)
    navigator.destroy_node()
    rclpy.shutdown()


if __name__ == '__main__':
    main()