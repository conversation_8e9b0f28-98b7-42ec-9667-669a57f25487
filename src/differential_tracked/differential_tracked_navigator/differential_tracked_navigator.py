#!/usr/bin/env python3
"""
差分履带车导航控制器
====================

这是一个基于ROS2的差分履带车导航控制系统，实现了以下功能：
1. PID控制器用于精确的速度和角度控制
2. 路径点导航和自动路径跟踪
3. 激光雷达障碍物检测和避障
4. 里程计健康监测和异常处理
5. 位置跳跃检测和恢复机制
6. 平滑的控制过渡和安全停车

作者：导航系统开发团队
版本：2.0
"""

import rclpy
from rclpy.node import Node
from rclpy.qos import QoSProfile, ReliabilityPolicy, DurabilityPolicy
import math
import os
import time

# ROS2消息类型导入
from geometry_msgs.msg import Twist, TwistStamped, PointStamped, PoseStamped  # 速度控制、路径点和姿态消息
from nav_msgs.msg import Odometry, OccupancyGrid, Path    # 里程计、地图和路径消息
from sensor_msgs.msg import PointCloud2             # 激光雷达点云消息
from std_msgs.msg import Empty, Int8               # 空消息类型用于状态信号，Int8用于启动/停止信号
import sensor_msgs_py.point_cloud2 as pc2          # 点云处理工具

# 尝试导入触发服务，如果不可用则回退
try:
    from std_srvs.srv import Trigger
    TRIGGER_AVAILABLE = True
except ImportError:
    TRIGGER_AVAILABLE = False


class PIDController:
    """
    PID控制器实现
    ============

    用于车辆控制的PID（比例-积分-微分）控制器。
    提供平滑、稳定的控制输出，具有积分饱和保护功能。

    参数:
        kp: 比例增益 - 控制响应速度
        ki: 积分增益 - 消除稳态误差
        kd: 微分增益 - 减少超调和振荡
        dt: 采样时间间隔
        max_integral: 积分项最大值，防止积分饱和
    """

    def __init__(self, kp=1.0, ki=0.0, kd=0.0, dt=0.1, max_integral=10.0):
        # PID参数设置
        self.kp = kp                    # 比例增益
        self.ki = ki                    # 积分增益
        self.kd = kd                    # 微分增益
        self.dt = dt                    # 时间步长
        self.max_integral = max_integral # 积分项上限，防止积分饱和

        # 控制器状态变量
        self.prev_error = 0.0           # 上一次的误差值
        self.integral = 0.0             # 积分累积值
        self.last_time = None           # 上一次计算的时间戳

    def compute(self, error, current_time=None):
        """
        计算PID控制器输出
        ================

        根据输入误差计算PID控制输出，包含比例、积分、微分三个分量。

        参数:
            error: 当前误差值（目标值 - 实际值）
            current_time: 当前时间戳，如果为None则使用系统时间

        返回:
            float: PID控制器的输出值
        """
        if current_time is None:
            current_time = time.time()

        # 首次调用时的初始化处理
        if self.last_time is None:
            self.last_time = current_time
            self.dt = 0.1  # 默认时间步长
            # 首次调用只返回比例项，避免微分项突变
            output = self.kp * error
            self.prev_error = error
            return output
        else:
            # 计算实际时间间隔
            self.dt = current_time - self.last_time

        # 避免除零错误和过小的时间间隔
        if self.dt <= 0.001:  # 最小1毫秒
            self.dt = 0.1

        # 计算PID各项
        # 积分项：累积误差
        self.integral += error * self.dt

        # 积分饱和保护：限制积分项范围，防止积分饱和
        self.integral = max(-self.max_integral, min(self.max_integral, self.integral))

        # 微分项：误差变化率
        derivative = (error - self.prev_error) / self.dt if self.dt > 0 else 0.0

        # 计算最终输出：比例项 + 积分项 + 微分项
        output = self.kp * error + self.ki * self.integral + self.kd * derivative

        # 保存当前值供下次迭代使用
        self.prev_error = error
        self.last_time = current_time

        return output

    def reset(self):
        """
        重置PID控制器状态
        ================

        清除所有历史状态，用于控制器重新启动或切换目标时。
        """
        self.prev_error = 0.0
        self.integral = 0.0
        self.last_time = None

    def set_gains(self, kp, ki, kd):
        """
        更新PID增益参数
        ==============

        动态调整PID控制器的增益参数。

        参数:
            kp: 新的比例增益
            ki: 新的积分增益
            kd: 新的微分增益
        """
        self.kp = kp
        self.ki = ki
        self.kd = kd


class VehicleParameters:
    """
    差分履带车配置参数类
    ==================

    定义了差分履带车的物理参数、控制参数和安全参数。
    这些参数可以通过ROS2参数服务器进行动态配置。
    """
    def __init__(self):
        # === 车辆物理参数 ===
        self.wheelbase = 0.5            # 前后轴距离（米）
        self.track_width = 0.4          # 左右履带间距离（米）
        self.max_linear_velocity = 2.0  # 最大线速度（米/秒）
        self.max_angular_velocity = 1.5 # 最大角速度（弧度/秒）
        self.min_linear_velocity = 0.2  # 最小线速度（米/秒）- 为转向需求而增加
        self.wheel_radius = 0.1         # 车轮半径（米）

        # === 控制参数 ===
        self.goal_tolerance = 0.4       # 到达路径点的距离容差（米）
        self.control_frequency = 10.0   # 控制循环频率（赫兹）

        # === PID控制参数 ===
        # 线速度PID参数
        self.linear_kp = 2.0            # 线速度比例增益
        self.linear_ki = 0.1            # 线速度积分增益
        self.linear_kd = 0.05           # 线速度微分增益

        # 角速度PID参数
        self.angular_kp = 0.8           # 角速度比例增益
        self.angular_ki = 0.05           # 角速度积分增益
        self.angular_kd = 0.1           # 角速度微分增益

        # === 安全参数 ===
        self.obstacle_detection_range = 2.0   # 障碍物检测范围（米）
        self.safety_stop_distance = 0.5      # 安全停车距离（米）
        self.emergency_stop_distance = 0.3   # 紧急停车距离（米）
        self.enable_obstacle_detection = False # 启用/禁用障碍物检测


class DifferentialTrackedNavigator(Node):
    """
    差分履带车ROS2导航控制器
    ========================

    这是主要的导航控制器类，继承自ROS2 Node。
    实现了完整的自主导航功能，包括：
    - 路径点跟踪
    - PID速度控制
    - 障碍物避障
    - 里程计健康监测
    - 异常恢复机制
    """

    def __init__(self):
        super().__init__('differential_tracked_navigator')

        # === 初始化车辆参数 ===
        self.vehicle_params = VehicleParameters()
        self._declare_parameters()  # 声明ROS2参数
        self._load_parameters()     # 加载参数值

        # === 初始化PID控制器（带积分饱和保护）===
        dt = 1.0 / self.vehicle_params.control_frequency  # 控制周期
        max_integral = getattr(self.vehicle_params, 'max_integral_windup', 5.0)  # 积分上限

        # 线速度PID控制器
        self.linear_pid = PIDController(
            self.vehicle_params.linear_kp,
            self.vehicle_params.linear_ki,
            self.vehicle_params.linear_kd,
            dt,
            max_integral
        )

        # 角速度PID控制器
        self.angular_pid = PIDController(
            self.vehicle_params.angular_kp,
            self.vehicle_params.angular_ki,
            self.vehicle_params.angular_kd,
            dt,
            max_integral
        )

        # === 状态变量初始化 ===
        self.current_pose = None            # 当前位姿（来自里程计）
        self.current_velocity = None        # 当前速度（来自里程计）
        self.current_local_goal = None      # 当前局部目标（来自全局轨迹规划器）
        self.is_navigating = False          # 导航状态标志
        self.obstacle_detected = False      # 障碍物检测标志
        self.emergency_stop = False         # 紧急停车标志
        self.map_data = None               # 地图数据
        self.navigation_stopped = False     # 导航停止标志
        
        # === 里程计监测相关变量 ===
        self.last_odometry_time = None          # 最后一次收到里程计数据的时间
        self.odometry_timeout = 2.0             # 里程计超时时间（秒）
        self.odometry_lost_warned = False       # 里程计丢失警告标志
        self.startup_grace_period = 10.0        # 启动后的宽限期（秒）
        self.node_start_time = self.get_clock().now()  # 节点启动时间
        self.consecutive_bad_odom_count = 0     # 连续坏里程计数据计数
        self.max_consecutive_bad_odom = 3       # 停车前允许的最大连续坏数据数

        # === 导航恢复状态 ===
        self.navigation_paused_due_to_odom_loss = False  # 因里程计丢失而暂停导航
        self.was_navigating_before_odom_loss = False     # 里程计丢失前是否在导航

        # === 位置跳跃检测 ===
        self.previous_pose = None               # 上一次的位姿
        self.position_jump_threshold = 2.0      # 位置跳跃阈值（米）
        self.large_position_jump_detected = False  # 大位置跳跃检测标志
        self.position_stabilization_count = 0   # 位置稳定计数
        self.required_stable_readings = 5       # 信任位置前需要的稳定读数数量

        # === 初始定位检查 ===
        self.initial_position_set = False      # 初始位置设置标志

        # === 角速度平滑相关变量 ===
        self.prev_angular_vel = 0.0            # 上一次的角速度

        # === 恢复控制状态 ===
        self.recovery_mode = False             # 恢复模式标志
        self.recovery_start_time = None        # 恢复开始时间

        # === 路径点过渡控制状态 ===
        self.waypoint_transition_mode = False  # 路径点过渡模式标志
        self.waypoint_transition_start_time = None  # 过渡开始时间
        self.waypoint_transition_duration = 2.0     # 路径点切换后的温和控制时间（秒）
        
        # === 路径点变更检测 ===
        self.previous_local_goal = None  # 上一个局部目标，用于检测路径点变更

        # === 初始化ROS2通信接口 ===
        self._setup_publishers()   # 设置发布者
        self._setup_subscribers()  # 设置订阅者
        self._setup_services()     # 设置服务

        # === 启动控制定时器 ===
        control_period = 1.0 / self.vehicle_params.control_frequency  # 控制周期
        self.control_timer = self.create_timer(control_period, self._control_loop)

        self.get_logger().info('差分履带车PID导航控制器初始化完成 - 等待路径数据')

    def _declare_parameters(self):
        """
        声明ROS2参数
        ============

        声明所有可配置的ROS2参数，包括车辆物理参数、控制参数、
        安全参数等。这些参数可以通过launch文件或命令行进行配置。
        """
        # === 车辆物理参数 ===
        self.declare_parameter('vehicle.wheelbase', self.vehicle_params.wheelbase)
        self.declare_parameter('vehicle.track_width', self.vehicle_params.track_width)
        self.declare_parameter('vehicle.max_linear_velocity', self.vehicle_params.max_linear_velocity)
        self.declare_parameter('vehicle.max_angular_velocity', self.vehicle_params.max_angular_velocity)
        self.declare_parameter('vehicle.min_linear_velocity', self.vehicle_params.min_linear_velocity)
        self.declare_parameter('vehicle.wheel_radius', self.vehicle_params.wheel_radius)

        # === 基本控制参数 ===
        self.declare_parameter('control.goal_tolerance', self.vehicle_params.goal_tolerance)
        self.declare_parameter('control.control_frequency', self.vehicle_params.control_frequency)
        self.declare_parameter('control.fast_turn_mode', False)  # 快速转向模式

        # === PID控制参数 ===
        # 线速度PID参数
        self.declare_parameter('control.linear_kp', self.vehicle_params.linear_kp)
        self.declare_parameter('control.linear_ki', self.vehicle_params.linear_ki)
        self.declare_parameter('control.linear_kd', self.vehicle_params.linear_kd)
        # 角速度PID参数
        self.declare_parameter('control.angular_kp', self.vehicle_params.angular_kp)
        self.declare_parameter('control.angular_ki', self.vehicle_params.angular_ki)
        self.declare_parameter('control.angular_kd', self.vehicle_params.angular_kd)

        # === PID安全参数 ===
        self.declare_parameter('control.max_integral_windup', 5.0)    # 积分饱和上限
        self.declare_parameter('control.enable_integral_reset', True) # 启用积分重置

        # === 安全参数 ===
        self.declare_parameter('safety.obstacle_detection_range', self.vehicle_params.obstacle_detection_range)
        self.declare_parameter('safety.safety_stop_distance', self.vehicle_params.safety_stop_distance)
        self.declare_parameter('safety.emergency_stop_distance', self.vehicle_params.emergency_stop_distance)
        self.declare_parameter('safety.enable_obstacle_detection', self.vehicle_params.enable_obstacle_detection)

        # === 里程计健康监测参数 ===
        self.declare_parameter('odometry.enable_health_check', True)        # 启用健康检查
        self.declare_parameter('odometry.timeout', 3.0)                    # 超时时间
        self.declare_parameter('odometry.startup_grace_period', 15.0)      # 启动宽限期
        self.declare_parameter('odometry.max_consecutive_bad_readings', 5) # 最大连续坏读数
        self.declare_parameter('odometry.max_position_jump', 2.0)          # 最大位置跳跃
        self.declare_parameter('odometry.max_velocity_bound', 5.0)         # 最大速度界限
        self.declare_parameter('odometry.max_angular_velocity_bound', 5.0) # 最大角速度界限

        # === 导航恢复参数 ===
        self.declare_parameter('navigation.auto_resume_after_odom_recovery', True)  # 里程计恢复后自动恢复导航
        self.declare_parameter('navigation.recalculate_waypoint_on_recovery', True) # 恢复时重新计算路径点
        self.declare_parameter('navigation.max_waypoint_skip_distance', 1.0)       # 最大路径点跳过距离

        # === 位置跳跃检测参数 ===
        self.declare_parameter('navigation.enable_position_jump_detection', True)  # 启用位置跳跃检测
        self.declare_parameter('navigation.position_jump_threshold', 2.0)         # 位置跳跃阈值
        self.declare_parameter('navigation.required_stable_readings', 5)          # 需要的稳定读数数量
        self.declare_parameter('navigation.max_reasonable_distance', 20.0)        # 最大合理距离
        self.declare_parameter('navigation.emergency_angular_limit', 0.5)         # 紧急角速度限制
        self.declare_parameter('navigation.emergency_linear_limit', 0.5)          # 紧急线速度限制

        # === 恢复控制参数 ===
        self.declare_parameter('navigation.recovery_duration', 3.0)               # 恢复持续时间
        self.declare_parameter('navigation.recovery_min_authority', 0.3)          # 恢复最小权限
        self.declare_parameter('navigation.recovery_angle_tolerance', 45.0)       # 恢复角度容差

        # === 路径点过渡参数 ===
        self.declare_parameter('navigation.waypoint_transition_duration', 2.0)    # 路径点过渡持续时间
        self.declare_parameter('navigation.waypoint_transition_min_authority', 0.4) # 过渡最小权限

    def _load_parameters(self):
        """
        从ROS2参数服务器加载参数
        ========================

        读取所有已声明的参数值并更新到相应的变量中。
        这允许在运行时动态配置车辆行为。
        """
        # === 加载车辆物理参数 ===
        self.vehicle_params.wheelbase = self.get_parameter('vehicle.wheelbase').value
        self.vehicle_params.track_width = self.get_parameter('vehicle.track_width').value
        self.vehicle_params.max_linear_velocity = self.get_parameter('vehicle.max_linear_velocity').value
        self.vehicle_params.max_angular_velocity = self.get_parameter('vehicle.max_angular_velocity').value
        self.vehicle_params.min_linear_velocity = self.get_parameter('vehicle.min_linear_velocity').value
        self.vehicle_params.wheel_radius = self.get_parameter('vehicle.wheel_radius').value

        # === 加载基本控制参数 ===
        self.vehicle_params.goal_tolerance = self.get_parameter('control.goal_tolerance').value
        self.vehicle_params.control_frequency = self.get_parameter('control.control_frequency').value

        # 加载快速转向模式
        self.fast_turn_mode = self.get_parameter('control.fast_turn_mode').value

        # === 加载PID控制参数 ===
        self.vehicle_params.linear_kp = self.get_parameter('control.linear_kp').value
        self.vehicle_params.linear_ki = self.get_parameter('control.linear_ki').value
        self.vehicle_params.linear_kd = self.get_parameter('control.linear_kd').value
        self.vehicle_params.angular_kp = self.get_parameter('control.angular_kp').value
        self.vehicle_params.angular_ki = self.get_parameter('control.angular_ki').value
        self.vehicle_params.angular_kd = self.get_parameter('control.angular_kd').value

        # === 加载PID安全参数 ===
        self.vehicle_params.max_integral_windup = self.get_parameter('control.max_integral_windup').value
        self.vehicle_params.enable_integral_reset = self.get_parameter('control.enable_integral_reset').value

        # === 加载安全参数 ===
        self.vehicle_params.obstacle_detection_range = self.get_parameter('safety.obstacle_detection_range').value
        self.vehicle_params.safety_stop_distance = self.get_parameter('safety.safety_stop_distance').value
        self.vehicle_params.emergency_stop_distance = self.get_parameter('safety.emergency_stop_distance').value
        self.vehicle_params.enable_obstacle_detection = self.get_parameter('safety.enable_obstacle_detection').value

        # === 加载里程计健康监测参数 ===
        self.enable_odometry_health_check = self.get_parameter('odometry.enable_health_check').value
        self.odometry_timeout = self.get_parameter('odometry.timeout').value
        self.startup_grace_period = self.get_parameter('odometry.startup_grace_period').value
        self.max_consecutive_bad_odom = self.get_parameter('odometry.max_consecutive_bad_readings').value
        self.max_position_jump = self.get_parameter('odometry.max_position_jump').value
        self.max_velocity_bound = self.get_parameter('odometry.max_velocity_bound').value
        self.max_angular_velocity_bound = self.get_parameter('odometry.max_angular_velocity_bound').value

        # === 加载导航恢复参数 ===
        self.auto_resume_after_odom_recovery = self.get_parameter('navigation.auto_resume_after_odom_recovery').value
        self.recalculate_waypoint_on_recovery = self.get_parameter('navigation.recalculate_waypoint_on_recovery').value
        self.max_waypoint_skip_distance = self.get_parameter('navigation.max_waypoint_skip_distance').value

        # === 加载位置跳跃检测参数 ===
        self.enable_position_jump_detection = self.get_parameter('navigation.enable_position_jump_detection').value
        self.position_jump_threshold = self.get_parameter('navigation.position_jump_threshold').value
        self.required_stable_readings = self.get_parameter('navigation.required_stable_readings').value
        self.max_reasonable_distance = self.get_parameter('navigation.max_reasonable_distance').value
        self.emergency_angular_limit = self.get_parameter('navigation.emergency_angular_limit').value
        self.emergency_linear_limit = self.get_parameter('navigation.emergency_linear_limit').value

        # === 加载恢复控制参数 ===
        self.recovery_duration = self.get_parameter('navigation.recovery_duration').value
        self.recovery_min_authority = self.get_parameter('navigation.recovery_min_authority').value
        self.recovery_angle_tolerance_deg = self.get_parameter('navigation.recovery_angle_tolerance').value

        # === 加载路径点过渡参数 ===
        self.waypoint_transition_duration = self.get_parameter('navigation.waypoint_transition_duration').value
        self.waypoint_transition_min_authority = self.get_parameter('navigation.waypoint_transition_min_authority').value

        # === 记录安全检查状态 ===
        if not self.enable_odometry_health_check:
            self.get_logger().warn('⚠️  里程计健康检查已禁用 - 无里程计丢失检测')

        if not self.enable_position_jump_detection:
            self.get_logger().warn('⚠️  位置跳跃检测已禁用 - 无位置跳跃保护')

        if not self.enable_odometry_health_check and not self.enable_position_jump_detection:
            self.get_logger().warn('⚠️  所有安全检查已禁用 - 运行在无限制模式')
        else:
            self.get_logger().info('安全检查配置成功')

    def _setup_publishers(self):
        """
        设置ROS2发布者（根据导航-To-APP接口调整）
        =============

        创建用于发布控制命令和状态信息的ROS2发布者。
        """
        # 车辆控制命令发布者 - 发布速度控制指令（TwistStamped用于仿真器兼容性）
        self.cmd_vel_publisher = self.create_publisher(
            TwistStamped,   # 消息类型：包含时间戳的线速度和角速度（仿真器需要）
            '/cmd_vel',     # 话题名称：标准的速度控制话题
            10              # 队列大小
        )

        # 路径点发布者 - 发布当前目标路径点（保持原有功能）
        self.waypoint_publisher = self.create_publisher(
            PointStamped,   # 消息类型：带时间戳的3D点
            '/way_point',   # 话题名称：当前目标路径点
            10              # 队列大小
        )
        
        # === 根据接口要求添加的发布者 ===
        # 车辆到达目标后发布/stopAtTarget
        self.stop_at_target_publisher = self.create_publisher(
            Empty,          # 消息类型：空消息
            '/stopAtTarget', # 话题名称：到达目标信号
            10              # 队列大小
        )
        
        # 遇到障碍物时发布/stop
        self.stop_publisher = self.create_publisher(
            Empty,          # 消息类型：空消息
            '/stop',        # 话题名称：障碍物停车信号
            10              # 队列大小
        )
        
        self.get_logger().info('发布者初始化完成')

    def _setup_subscribers(self):
        """
        设置ROS2订阅者（根据导航-To-APP接口调整）
        =============

        创建用于接收传感器数据和状态信息的ROS2订阅者。
        """
        # 状态估计订阅者 - 接收车辆位姿和速度信息
        self.state_estimation_subscriber = self.create_subscription(
            Odometry,                       # 消息类型：里程计数据
            '/state_estimation',            # 话题名称：状态估计话题
            self._state_estimation_callback, # 回调函数
            10                              # 队列大小
        )

        # === 根据接口要求添加的订阅者 ===
        # 接收/local_goal局部目标点
        self.local_goal_subscriber = self.create_subscription(
            PoseStamped,                    # 消息类型：带时间戳的姿态
            '/local_goal',                  # 话题名称：局部目标点
            self._local_goal_callback,      # 回调函数
            10                              # 队列大小
        )
        
        # 接收/stop_navigation停止导航信号
        self.stop_navigation_subscriber = self.create_subscription(
            Int8,                           # 消息类型：Int8消息
            '/stop_navigation',             # 话题名称：停止导航信号
            self._stop_navigation_callback, # 回调函数
            10                              # 队列大小
        )

        # 激光雷达数据订阅者 - 接收点云数据用于障碍物检测
        self.lidar_subscriber = self.create_subscription(
            PointCloud2,                    # 消息类型：点云数据
            '/registered_scan',             # 话题名称：配准后的激光扫描
            self._lidar_callback,           # 回调函数
            10                              # 队列大小
        )

        # 地图订阅者 - 接收占用栅格地图（用于地图服务器集成）
        map_qos = QoSProfile(
            reliability=ReliabilityPolicy.RELIABLE,        # 可靠传输
            durability=DurabilityPolicy.TRANSIENT_LOCAL,   # 持久化存储
            depth=1                                         # 队列深度
        )

        self.map_subscriber = self.create_subscription(
            OccupancyGrid,                  # 消息类型：占用栅格地图
            '/map',                         # 话题名称：地图话题
            self._map_callback,             # 回调函数
            map_qos                         # QoS配置
        )

        self.get_logger().info('订阅者初始化完成')

    def _setup_services(self):
        """
        设置ROS2服务
        ===========

        创建用于手动控制和状态查询的ROS2服务。
        """
        if TRIGGER_AVAILABLE:
            # 手动恢复导航服务 - 允许外部手动恢复导航
            self.resume_navigation_service = self.create_service(
                Trigger,                        # 服务类型：触发服务
                'resume_navigation',            # 服务名称
                self._resume_navigation_callback # 回调函数
            )
            self.get_logger().info('服务初始化完成')
        else:
            self.get_logger().warn('std_srvs.srv.Trigger不可用，手动恢复服务已禁用')

    def _local_goal_callback(self, msg: PoseStamped):
        """
        局部目标回调函数（根据导航-To-APP接口调整）
        ===============

        接收来自全局轨迹规划器的局部目标点。
        替代原来的导航结果回调功能。
        增加了路径点变更检测和PID重置功能。

        参数:
            msg: 局部目标消息，包含目标位置和姿态信息
        """
        # 检测是否为新的路径点
        waypoint_changed = False
        if self.previous_local_goal is not None:
            # 计算与上一个目标点的距离
            prev_x = self.previous_local_goal.pose.position.x
            prev_y = self.previous_local_goal.pose.position.y
            curr_x = msg.pose.position.x
            curr_y = msg.pose.position.y
            
            distance_change = math.sqrt((curr_x - prev_x)**2 + (curr_y - prev_y)**2)
            
            # 如果距离变化超过阈值，认为是新的路径点
            if distance_change > 0.5:  # 0.5米阈值
                waypoint_changed = True
                self.get_logger().info(
                    f'路径点变更检测: 从({prev_x:.2f}, {prev_y:.2f}) 到 ({curr_x:.2f}, {curr_y:.2f}), '
                    f'距离变化: {distance_change:.2f}m'
                )
        else:
            # 第一次接收目标点
            waypoint_changed = True
        
        # 如果路径点发生变更，重置PID控制器并激活过渡模式
        if waypoint_changed:
            self.get_logger().info('路径点变更 - 重置PID控制器并激活过渡模式')
            self._reset_pid_controllers()
            
            # 激活路径点过渡模式
            self.waypoint_transition_mode = True
            self.waypoint_transition_start_time = self.get_clock().now()
            
            self.get_logger().info(
                f'激活路径点过渡模式: 持续时间={self.waypoint_transition_duration:.1f}s, '
                f'最小控制权限={self.waypoint_transition_min_authority:.2f}'
            )
        
        # 保存当前目标点
        self.previous_local_goal = msg
        self.current_local_goal = msg
        
        # 启动导航（如果未启动）
        if not self.is_navigating and not self.navigation_stopped:
            self.is_navigating = True
            self.get_logger().info(f'收到局部目标，开始导航到: ({msg.pose.position.x:.2f}, {msg.pose.position.y:.2f})')
        elif not self.navigation_stopped:
            self.get_logger().debug(f'更新局部目标到: ({msg.pose.position.x:.2f}, {msg.pose.position.y:.2f})')
        
        # 发布当前目标路径点（用于可视化）
        self._publish_current_waypoint()
    
    def _stop_navigation_callback(self, msg: Int8):
        """
        停止导航回调函数（根据导航-To-APP接口调整）
        ===============
        
        接收/stop_navigation信号停止发布控制数据。
        
        参数:
            msg: Int8消息
        """
        # 根据接口要求：接收终止导航/stop_navigation不再发布控制数据
        if not self.navigation_stopped:
            self.navigation_stopped = True
            self.is_navigating = False
            self.current_local_goal = None
            
            # 停止车辆
            self._stop_vehicle()
            
            self.get_logger().info('收到停止导航信号 - 停止发布控制数据')
        else:
            self.get_logger().info('收到停止导航信号，但导航已停止')

    def _state_estimation_callback(self, msg: Odometry):
        """
        状态估计回调函数
        ===============

        处理来自定位系统的里程计数据，包括位姿和速度信息。
        实现了多层安全检查：数据有效性验证和位置跳跃检测。

        参数:
            msg: 里程计消息，包含位姿和速度信息
        """
        # 如果所有检查都被禁用，直接接受数据
        if not self.enable_odometry_health_check and not self.enable_position_jump_detection:
            self.current_pose = msg.pose.pose
            self.current_velocity = msg.twist.twist
            self.last_odometry_time = self.get_clock().now()
            return

        # 验证里程计数据质量（如果启用健康检查）
        data_valid = True
        if self.enable_odometry_health_check:
            data_valid = self._validate_odometry_data(msg)

        if data_valid:
            # 检查位置跳跃（如果启用跳跃检测）
            position_ok = True
            if self.enable_position_jump_detection:
                position_ok = self._check_position_jump(msg.pose.pose)

            if position_ok:
                # 数据有效且位置正常，更新当前状态
                self.current_pose = msg.pose.pose
                self.current_velocity = msg.twist.twist

                # 更新里程计时间戳并重置警告标志（如果启用健康检查）
                if self.enable_odometry_health_check:
                    self.last_odometry_time = self.get_clock().now()
                    self.consecutive_bad_odom_count = 0  # 重置坏数据计数

                    if self.odometry_lost_warned:
                        self.get_logger().info('里程计数据已恢复')
                        self.odometry_lost_warned = False

                        # 在下一个控制循环中触发导航恢复检查
                        if self.navigation_paused_due_to_odom_loss:
                            self.get_logger().info('里程计已恢复 - 导航即将恢复')
            else:
                # 检测到位置跳跃，不更新位姿但计为坏数据
                if self.enable_odometry_health_check:
                    self.consecutive_bad_odom_count += 1
                    self.get_logger().warn(f'检测到位置跳跃，忽略里程计更新 (计数: {self.consecutive_bad_odom_count})')
        else:
            # 无效数据
            if self.enable_odometry_health_check:
                self.consecutive_bad_odom_count += 1
                self.get_logger().warn(f'收到无效里程计数据 (计数: {self.consecutive_bad_odom_count})')

    def _lidar_callback(self, msg: PointCloud2):
        """
        激光雷达数据回调函数
        ==================

        处理激光雷达点云数据，进行实时障碍物检测。
        根据障碍物距离设置不同级别的安全响应。

        参数:
            msg: 激光雷达点云消息
        """
        # 如果障碍物检测被禁用，跳过处理
        if not self.vehicle_params.enable_obstacle_detection:
            self.obstacle_detected = False
            self.emergency_stop = False
            return

        try:
            # 重置障碍物检测标志
            self.obstacle_detected = False
            self.emergency_stop = False

            # 将PointCloud2转换为点列表
            points = list(pc2.read_points(msg, field_names=("x", "y", "z"), skip_nans=True))

            # 在安全区域内检查障碍物
            for point in points:
                x, y, _ = point  # 2D导航忽略z坐标
                distance = math.sqrt(x**2 + y**2)  # 计算到车辆的距离

                # 检查点是否在检测范围内且在车辆前方
                if (distance < self.vehicle_params.obstacle_detection_range and
                    x > 0 and  # 只考虑前方的点
                    abs(y) < self.vehicle_params.track_width / 2 + 0.2):  # 在车辆宽度加余量范围内

                    # 根据距离设置不同的安全响应级别
                    if distance < self.vehicle_params.emergency_stop_distance:
                        self.emergency_stop = True  # 紧急停车
                        break
                    elif distance < self.vehicle_params.safety_stop_distance:
                        self.obstacle_detected = True  # 安全停车

        except Exception as e:
            self.get_logger().warn(f'处理激光雷达数据时出错: {e}')

    def _map_callback(self, msg: OccupancyGrid):
        """
        地图数据回调函数
        ===============

        接收并存储占用栅格地图数据，用于路径规划和障碍物检测。

        参数:
            msg: 占用栅格地图消息
        """
        self.map_data = msg
        self.get_logger().info('已接收地图数据')

    def _publish_current_waypoint(self):
        """
        发布当前目标路径点（根据导航-To-APP接口调整）
        =================

        将当前正在导航的局部目标点发布到ROS话题，
        供其他节点（如可视化工具）使用。
        """
        if self.current_local_goal:
            # 创建带时间戳的点消息
            point_msg = PointStamped()
            point_msg.header.stamp = self.get_clock().now().to_msg()  # 当前时间戳
            point_msg.header.frame_id = 'map'                         # 坐标系
            point_msg.point.x = float(self.current_local_goal.pose.position.x)  # X坐标
            point_msg.point.y = float(self.current_local_goal.pose.position.y)  # Y坐标
            point_msg.point.z = float(self.current_local_goal.pose.position.z)  # Z坐标

            self.waypoint_publisher.publish(point_msg)

    def _control_loop(self):
        """
        主控制循环
        ==========

        这是导航系统的核心控制循环，以固定频率运行。
        负责：
        1. 里程计健康检查
        2. 导航恢复处理
        3. 路径点跟踪控制
        4. 安全监测和响应
        """
        # === 里程计数据丢失检查（仅在启用时） ===
        if self.enable_odometry_health_check:
            if not self._check_odometry_health():
                # 关键：里程计丢失时重置PID控制器以防止控制不稳定
                if self.is_navigating and not self.navigation_paused_due_to_odom_loss:
                    self.get_logger().warn('因里程计丢失而暂停导航')
                    self.was_navigating_before_odom_loss = True
                    self.navigation_paused_due_to_odom_loss = True

                self._reset_pid_controllers()  # 重置PID控制器
                self._stop_vehicle()           # 停止车辆
                return

        # === 里程计恢复后的导航恢复检查 ===
        if (self.navigation_paused_due_to_odom_loss and
            self.was_navigating_before_odom_loss and
            self.auto_resume_after_odom_recovery):

            self.get_logger().info('里程计恢复后恢复导航')
            self.navigation_paused_due_to_odom_loss = False
            self.was_navigating_before_odom_loss = False

            # 进入恢复模式以实现温和控制
            self.recovery_mode = True
            self.recovery_start_time = self.get_clock().now()

            # 重置PID控制器以实现干净的重启
            self._reset_pid_controllers()

            # 可选：位置跳跃后重新计算最近的路径点
            if self.recalculate_waypoint_on_recovery:
                self._recalculate_target_waypoint()

        # === 基本状态检查 ===
        if not self.is_navigating or not self.current_pose or not self.current_local_goal or self.navigation_stopped:
            return
            
        # 初始位置检查 - 如果起始位置距离目标点很远则警告
        if not self.initial_position_set and self.current_pose:
            target_x = self.current_local_goal.pose.position.x
            target_y = self.current_local_goal.pose.position.y
            current_x = self.current_pose.position.x
            current_y = self.current_pose.position.y
            initial_distance = math.sqrt((target_x - current_x)**2 + (target_y - current_y)**2)
            
            if initial_distance > 10.0:  # 距离目标点超过10米
                self.get_logger().warn(
                    f'初始位置距离目标点较远: {initial_distance:.2f}m. '
                    f'当前位置: ({current_x:.2f}, {current_y:.2f}), '
                    f'目标点: ({target_x:.2f}, {target_y:.2f})'
                )
            self.initial_position_set = True

        # Additional safety check: Don't navigate if position seems unreliable (only if detection enabled)
        if self.enable_position_jump_detection and self.large_position_jump_detected:
            self.get_logger().debug('Position unstable, skipping navigation control')
            return
        
        # 检查紧急停车
        if self.vehicle_params.enable_obstacle_detection and self.emergency_stop:
            self.get_logger().warn('障碍物紧急停车激活!')
            self._stop_vehicle()
            return
        
        try:
            # === 检查是否到达当前目标点 ===
            if self._reached_current_target():
                # 已到达目标点
                self._stop_vehicle()
                self.is_navigating = False
                
                # 根据接口要求：车辆到达目的地后发布/stopAtTarget
                stop_at_target_msg = Empty()
                self.stop_at_target_publisher.publish(stop_at_target_msg)
                
                self.get_logger().info(f'已到达目标点 ({self.current_local_goal.pose.position.x:.2f}, {self.current_local_goal.pose.position.y:.2f}) - 发布停止信号')
                return
            
            # 使用PID计算控制命令
            cmd_vel = self._calculate_control_command()
            
            # === 应用安全约束和状态反馈 ===
            if self.vehicle_params.enable_obstacle_detection and self.obstacle_detected:
                self.get_logger().warn('检测到障碍物 - 停止车辆')
                
                # 根据接口要求：遇到障碍物时发布/stop
                stop_msg = Empty()
                self.stop_publisher.publish(stop_msg)
                
                self._stop_vehicle()
            elif not self.navigation_stopped:  # 只有在导航未停止时才发布控制命令
                self.cmd_vel_publisher.publish(cmd_vel)
            
        except Exception as e:
            self.get_logger().error(f'控制循环错误: {e}')
            # 发生任何错误时重置PID控制器以防止不稳定
            self._reset_pid_controllers()
            self._stop_vehicle()
    
    def _reached_current_target(self) -> bool:
        """检查车辆是否已到达当前目标点（根据新接口调整）"""
        if not self.current_local_goal or not self.current_pose:
            return False
        
        target_x = self.current_local_goal.pose.position.x
        target_y = self.current_local_goal.pose.position.y
        current_x = self.current_pose.position.x
        current_y = self.current_pose.position.y
        
        distance = math.sqrt((target_x - current_x)**2 + (target_y - current_y)**2)
        return distance < self.vehicle_params.goal_tolerance
    
    def _calculate_control_command(self) -> TwistStamped:
        """使用PID控制器计算控制命令（修改为TwistStamped用于仿真器兼容性）"""
        if not self.current_local_goal or not self.current_pose:
            # 返回空的TwistStamped消息
            cmd_msg = TwistStamped()
            cmd_msg.header.stamp = self.get_clock().now().to_msg()
            cmd_msg.header.frame_id = 'base_link'
            return cmd_msg

        # 安全检查：如果位置不稳定则不生成命令（仅在启用检测时）
        if self.enable_position_jump_detection and self.large_position_jump_detected:
            self.get_logger().debug('位置不稳定，发送零命令')
            cmd_msg = TwistStamped()
            cmd_msg.header.stamp = self.get_clock().now().to_msg()
            cmd_msg.header.frame_id = 'base_link'
            return cmd_msg

        target_x = self.current_local_goal.pose.position.x
        target_y = self.current_local_goal.pose.position.y
        current_x = self.current_pose.position.x
        current_y = self.current_pose.position.y

        # 从四元数计算当前偏航角
        current_yaw = self._quaternion_to_yaw(self.current_pose.orientation)

        # 计算到目标点的距离和角度
        dx = target_x - current_x
        dy = target_y - current_y
        distance_to_goal = math.sqrt(dx**2 + dy**2)

        # 安全检查：如果距离过大可能有问题（仅在启用检测时）
        if self.enable_position_jump_detection and distance_to_goal > self.max_reasonable_distance:
            self.get_logger().warn(
                f'到目标的距离过大: {distance_to_goal:.2f}m. '
                f'可能的里程计错误。发送零命令。'
            )
            return Twist()
        
        # Calculate target heading
        target_yaw = math.atan2(dy, dx)
        yaw_error = self._normalize_angle(target_yaw - current_yaw)
        
        # PID control implementation
        cmd_msg = TwistStamped()
        cmd_msg.header.stamp = self.get_clock().now().to_msg()
        cmd_msg.header.frame_id = 'base_link'
        
        # Check if we're in recovery mode or waypoint transition mode
        control_factor = 1.0
        current_time = self.get_clock().now()

        if self.recovery_mode:
            recovery_elapsed = (current_time - self.recovery_start_time).nanoseconds / 1e9

            if recovery_elapsed < self.recovery_duration:
                # Gradually increase control authority during recovery
                control_factor = min(1.0, recovery_elapsed / self.recovery_duration)
                control_factor = max(self.recovery_min_authority, control_factor)
                self.get_logger().debug(f'Recovery mode: factor={control_factor:.2f}, elapsed={recovery_elapsed:.1f}s')
            else:
                # Recovery period complete
                self.recovery_mode = False
                self.get_logger().info('Recovery mode complete, resuming full control authority')

        elif self.waypoint_transition_mode:
            transition_elapsed = (current_time - self.waypoint_transition_start_time).nanoseconds / 1e9

            if transition_elapsed < self.waypoint_transition_duration:
                # Gradually increase control authority during waypoint transition
                control_factor = min(1.0, transition_elapsed / self.waypoint_transition_duration)
                control_factor = max(self.waypoint_transition_min_authority, control_factor)
                self.get_logger().debug(f'Waypoint transition: factor={control_factor:.2f}, elapsed={transition_elapsed:.1f}s')
            else:
                # Transition period complete
                self.waypoint_transition_mode = False
                self.get_logger().info('Waypoint transition complete, resuming full control authority')

        # Angular velocity using PID control
        angular_vel = self.angular_pid.compute(yaw_error)
        
        # Apply fast turn mode boost for large yaw errors
        if self.fast_turn_mode and abs(yaw_error) > math.pi / 6:  # > 30 degrees
            # Boost angular response for faster turning
            boost_factor = min(1.5, 1.0 + abs(yaw_error) / math.pi)  # Up to 1.5x boost
            angular_vel *= boost_factor
            self.get_logger().debug(f'Fast turn mode: yaw_error={math.degrees(yaw_error):.1f}°, boost={boost_factor:.2f}')

        # Apply control factor to limit aggressive control during transitions
        if self.recovery_mode or self.waypoint_transition_mode:
            angular_vel *= control_factor

        # Safety limit for angular velocity based on error magnitude
        if abs(yaw_error) > math.pi:  # Error > 180 degrees indicates possible odometry issue
            self.get_logger().warn(f'Large yaw error detected: {yaw_error:.2f} rad. Limiting angular velocity.')
            angular_vel = math.copysign(min(abs(angular_vel), self.emergency_angular_limit), angular_vel)

        # Adjust angle tolerance based on recovery mode and distance
        base_angle_tolerance = math.pi / 4  # 45 degrees default
        
        # Be more permissive for close waypoints to avoid getting stuck
        if distance_to_goal < 3.0:  # Within 3 meters
            distance_factor = max(0.5, distance_to_goal / 3.0)
            angle_tolerance = base_angle_tolerance + (math.pi / 3) * (1.0 - distance_factor)  # Up to 105 degrees when very close
        else:
            angle_tolerance = base_angle_tolerance
            
        if self.recovery_mode:
            # More permissive angle tolerance during recovery
            angle_tolerance = max(angle_tolerance, math.radians(self.recovery_angle_tolerance_deg))

        # Linear velocity using PID control - improved alignment logic
        # Use more permissive alignment check for better forward motion
        extended_angle_tolerance = angle_tolerance * 1.5  # 50% more permissive
        
        if abs(yaw_error) < extended_angle_tolerance:
            # Use PID control for aligned or nearly aligned cases
            linear_vel = self.linear_pid.compute(distance_to_goal)
            
            self.get_logger().debug(
                f'Aligned/Nearly aligned: yaw_error={math.degrees(yaw_error):.1f}°, '
                f'tolerance={math.degrees(extended_angle_tolerance):.1f}°, '
                f'distance={distance_to_goal:.2f}m, linear_vel={linear_vel:.2f}'
            )

            # Apply control factor to linear velocity too
            if self.recovery_mode or self.waypoint_transition_mode:
                linear_vel *= control_factor

            # Safety limit for linear velocity based on distance
            # Use adaptive threshold based on waypoint spacing
            adaptive_threshold = min(self.max_reasonable_distance / 2, 20.0)  # Cap at 20m
            if distance_to_goal > adaptive_threshold:
                self.get_logger().warn(
                    f'Large distance error: {distance_to_goal:.2f}m (threshold: {adaptive_threshold:.1f}m). '
                    f'Limiting linear velocity to {self.emergency_linear_limit:.1f} m/s.'
                )
                linear_vel = min(linear_vel, self.emergency_linear_limit)

            # Improved speed reduction when turning - less aggressive and distance-aware
            if distance_to_goal > 2.0:  # Far from target - minimal speed reduction
                speed_factor = max(0.7, 1.0 - abs(yaw_error) / (math.pi / 2))  # Less aggressive
            else:  # Close to target - more careful speed reduction
                speed_factor = max(0.5, 1.0 - abs(yaw_error) / (math.pi / 3))
            linear_vel *= speed_factor
        else:
            # Not aligned - use dynamic forward motion based on angle and distance
            # Calculate base velocity based on distance with improved scaling
            if distance_to_goal > 3.0:  # Far from target
                base_vel = 0.5  # Increased from 0.4
            elif distance_to_goal > 1.5:  # Medium distance  
                base_vel = 0.3  # Increased from 0.2
            else:  # Close to target
                base_vel = 0.2  # Increased from 0.1
            
            # Apply angle-based scaling - more gradual reduction
            angle_factor = max(0.3, 1.0 - abs(yaw_error) / math.pi)  # More forgiving angle scaling
            linear_vel = base_vel * angle_factor
            
            # Ensure minimum forward motion when angle is reasonable
            if abs(yaw_error) < math.pi / 4:  # Less than 60 degrees
                linear_vel = max(linear_vel, 0.2)  # Minimum 0.2 m/s for reasonable angles - chassis requirement
            elif abs(yaw_error) < math.pi / 2:  # Less than 90 degrees  
                linear_vel = max(linear_vel, 0.2)  # Minimum 0.2 m/s - chassis requirement
            
            # In recovery or transition mode, ensure adequate forward motion
            if (self.recovery_mode or self.waypoint_transition_mode) and abs(yaw_error) < math.pi / 2:
                recovery_min_vel = 0.2 * control_factor  # Increased to 0.2 for chassis requirement
                linear_vel = max(linear_vel, recovery_min_vel)
                
            self.get_logger().debug(
                f'Not aligned: yaw_error={math.degrees(yaw_error):.1f}°, '
                f'distance={distance_to_goal:.2f}m, base_vel={base_vel:.2f}, '
                f'angle_factor={angle_factor:.2f}, linear_vel={linear_vel:.2f}'
            )
        
        # Apply velocity limits with absolute constraint against negative linear velocity
        # 绝对约束：防止负线速度，确保车辆始终向前运动
        linear_vel = max(0.0, linear_vel)  # 确保线速度非负
        
        cmd_msg.twist.linear.x = max(
            0.0,  # 绝对禁止负线速度
            min(
                self.vehicle_params.max_linear_velocity,
                linear_vel
            )
        )
        
        cmd_msg.twist.angular.z = max(
            -self.vehicle_params.max_angular_velocity,
            min(
                self.vehicle_params.max_angular_velocity,
                angular_vel
            )
        )
        
        # Apply angular velocity change rate limiting for smoother control
        max_angular_acceleration = 5.0  # rad/s² - increased for faster response
        dt = 1.0 / self.vehicle_params.control_frequency
        max_angular_change = max_angular_acceleration * dt
        
        angular_change = cmd_msg.twist.angular.z - self.prev_angular_vel
        if abs(angular_change) > max_angular_change:
            cmd_msg.twist.angular.z = self.prev_angular_vel + math.copysign(max_angular_change, angular_change)
            self.get_logger().debug(f'Angular velocity rate limited: change={angular_change:.3f}, max={max_angular_change:.3f}')
        
        # Update previous angular velocity for next iteration
        self.prev_angular_vel = cmd_msg.twist.angular.z
        
        # Apply distance-based angular velocity scaling for smoother approach
        # Only apply significant scaling when very close to target
        if distance_to_goal < 1.5:  # Within 1.5 meters of target (reduced from 3.0)
            distance_factor = max(0.5, distance_to_goal / 1.5)  # Scale down to minimum 50% (increased from 30%)
            cmd_msg.twist.angular.z *= distance_factor
            self.get_logger().debug(f'Distance-based angular scaling: factor={distance_factor:.2f}, distance={distance_to_goal:.2f}m')
        
        # Apply minimum velocity constraint - simplified and more robust
        # Skip minimum velocity constraints only when very close to target or in transition modes
        skip_min_velocity = (self.recovery_mode or self.waypoint_transition_mode or 
                             distance_to_goal < 0.3)  # Only skip when very close to target
        
        # Simplified minimum velocity logic to prevent conflicts with PID
        if cmd_msg.twist.linear.x > 0 and not skip_min_velocity:
            # Apply minimum velocity based on turning intensity
            if abs(cmd_msg.twist.angular.z) > 0.8:  # Very sharp turning
                min_vel = 0.05  # Very low minimum for sharp turns
            elif abs(cmd_msg.twist.angular.z) > 0.4:  # Moderate turning
                min_vel = 0.08  # Low minimum for moderate turns
            else:  # Straight or gentle turning
                min_vel = self.vehicle_params.min_linear_velocity * 0.6  # Reduced minimum
                
            if cmd_msg.twist.linear.x < min_vel:
                cmd_msg.twist.linear.x = min_vel
                self.get_logger().debug(f'Applied minimum velocity: {min_vel:.3f} m/s (angular: {cmd_msg.twist.angular.z:.3f})')

        # Final safety check: ensure no NaN or infinite values
        if not (math.isfinite(cmd_msg.twist.linear.x) and math.isfinite(cmd_msg.twist.angular.z)):
            self.get_logger().error('Invalid control command detected! Sending stop command.')
            self._reset_pid_controllers()
            # Return zero TwistStamped
            stop_msg = TwistStamped()
            stop_msg.header.stamp = self.get_clock().now().to_msg()
            stop_msg.header.frame_id = 'base_link'
            return stop_msg
        # 记录低/零线速度的详细控制信息（仅在调试时）
        if abs(cmd_msg.twist.linear.x) < 0.05:
            self.get_logger().debug(
                f'检测到低/零线速度: '
                f'距离={distance_to_goal:.2f}m, 偏航误差={math.degrees(yaw_error):.1f}°, '
                f'角度容差={math.degrees(angle_tolerance):.1f}°, '
                f'线速度={cmd_msg.twist.linear.x:.3f}, 角速度={cmd_msg.twist.angular.z:.3f}'
            )

        # Log control commands during recovery or transition for debugging
        if self.recovery_mode:
            self.get_logger().info(
                f'Recovery control: yaw_error={yaw_error:.3f}rad ({math.degrees(yaw_error):.1f}°), '
                f'distance={distance_to_goal:.2f}m, linear={cmd_msg.twist.linear.x:.3f}, angular={cmd_msg.twist.angular.z:.3f}, '
                f'factor={control_factor:.2f}'
            )
        elif self.waypoint_transition_mode:
            self.get_logger().info(
                f'路径点过渡: '
                f'偏航误差={yaw_error:.3f}弧度 ({math.degrees(yaw_error):.1f}°), '
                f'距离={distance_to_goal:.2f}m, 线速度={cmd_msg.twist.linear.x:.3f}, 角速度={cmd_msg.twist.angular.z:.3f}, '
                f'控制因子={control_factor:.2f}'
            )

        return cmd_msg

    def _stop_vehicle(self):
        """
        停止车辆
        ========

        发送停车命令给车辆，并确保命令被多次发布以保证安全。
        这是一个关键的安全函数，在紧急情况下使用。
        """
        stop_msg = TwistStamped()
        stop_msg.header.stamp = self.get_clock().now().to_msg()
        stop_msg.header.frame_id = 'base_link'
        stop_msg.twist.linear.x = 0.0   # 线速度设为0
        stop_msg.twist.angular.z = 0.0  # 角速度设为0

        # 多次发布停车命令以确保被接收
        for _ in range(3):
            self.cmd_vel_publisher.publish(stop_msg)

    def _reset_pid_controllers(self):
        """
        重置PID控制器
        =============

        重置PID控制器状态以防止积分饱和和控制不稳定。
        通常在里程计丢失、系统重启或路径点变更时调用。
        """
        self.linear_pid.reset()   # 重置线速度PID
        self.angular_pid.reset()  # 重置角速度PID
        
        # 重置相关状态变量
        self.prev_angular_vel = 0.0  # 重置上一次角速度
        
        self.get_logger().info('PID控制器已重置 - 清除所有累积误差和状态')

    def _recalculate_target_waypoint(self):
        """
        重新计算目标路径点
        =================

        在里程计恢复后记录目标信息，用于调试目的。
        由于现在使用单一目标点系统，无需重新计算。
        """
        if not self.current_pose or not self.current_local_goal:
            return

        current_x = self.current_pose.position.x
        current_y = self.current_pose.position.y
        target_x = self.current_local_goal.pose.position.x
        target_y = self.current_local_goal.pose.position.y

        distance = math.sqrt((target_x - current_x)**2 + (target_y - current_y)**2)
        
        self.get_logger().info(
            f'里程计恢复后继续导航到目标点 '
            f'(距离: {distance:.2f}m)'
        )

    def _quaternion_to_yaw(self, quat) -> float:
        """
        四元数转偏航角
        =============

        将四元数表示的姿态转换为偏航角（绕Z轴旋转）。

        参数:
            quat: 四元数对象，包含w, x, y, z分量

        返回:
            float: 偏航角（弧度），范围[-π, π]
        """
        siny_cosp = 2 * (quat.w * quat.z + quat.x * quat.y)
        cosy_cosp = 1 - 2 * (quat.y * quat.y + quat.z * quat.z)
        return math.atan2(siny_cosp, cosy_cosp)

    def _normalize_angle(self, angle: float) -> float:
        """
        角度归一化
        ==========

        将角度归一化到[-π, π]范围内。

        参数:
            angle: 输入角度（弧度）

        返回:
            float: 归一化后的角度（弧度）
        """
        while angle > math.pi:
            angle -= 2 * math.pi
        while angle < -math.pi:
            angle += 2 * math.pi
        return angle

    def _check_odometry_health(self) -> bool:
        """
        检查里程计健康状态
        =================

        检查里程计数据是否在超时期限内正常接收。
        包含启动宽限期处理，避免系统启动时的误报。

        返回:
            bool: True表示里程计健康，False表示存在问题
        """
        current_time = self.get_clock().now()

        # 检查是否仍在启动宽限期内
        time_since_startup = (current_time - self.node_start_time).nanoseconds / 1e9
        if time_since_startup < self.startup_grace_period:
            if self.last_odometry_time is None:
                # 仍在宽限期内，只警告但不停止
                if not self.odometry_lost_warned:
                    self.get_logger().info(f'等待里程计数据... ({time_since_startup:.1f}s/{self.startup_grace_period}s)')
                    self.odometry_lost_warned = True
                return False  # 暂不导航，但也不停止

        # 宽限期后，检查里程计数据
        if self.last_odometry_time is None:
            if not self.odometry_lost_warned:
                self.get_logger().warn('宽限期后未收到里程计数据')
                self.odometry_lost_warned = True
            # 不在此处调用_stop_vehicle()以避免递归
            return False

        # 检查数据超时
        time_since_last_odom = (current_time - self.last_odometry_time).nanoseconds / 1e9

        # Use dynamic timeout based on control frequency
        dynamic_timeout = max(self.odometry_timeout, 3.0 / self.vehicle_params.control_frequency)

        if time_since_last_odom > dynamic_timeout:
            if not self.odometry_lost_warned:
                self.get_logger().warn(f'Odometry data lost! No data received for {time_since_last_odom:.2f} seconds')
                self.odometry_lost_warned = True
            # Don't call _stop_vehicle() here to avoid recursion
            return False

        # Check for consecutive bad odometry readings
        if self.consecutive_bad_odom_count >= self.max_consecutive_bad_odom:
            if not self.odometry_lost_warned:
                self.get_logger().warn(f'Too many consecutive bad odometry readings: {self.consecutive_bad_odom_count}')
                self.odometry_lost_warned = True
            # Don't call _stop_vehicle() here to avoid recursion
            return False

        return True

    def _validate_odometry_data(self, msg: Odometry) -> bool:
        """Validate odometry data quality to prevent using corrupted data"""
        try:
            # Check for NaN or infinite values in position
            pose = msg.pose.pose.position
            if not (math.isfinite(pose.x) and math.isfinite(pose.y) and math.isfinite(pose.z)):
                self.get_logger().warn('Invalid position data: NaN or infinite values detected')
                return False

            # Check for reasonable position bounds (configurable)
            max_position = 1000.0  # meters - could be made configurable
            if (abs(pose.x) > max_position or abs(pose.y) > max_position or abs(pose.z) > max_position):
                self.get_logger().warn(f'Position out of bounds: x={pose.x:.2f}, y={pose.y:.2f}, z={pose.z:.2f}')
                return False

            # Check orientation quaternion validity
            quat = msg.pose.pose.orientation
            if not (math.isfinite(quat.x) and math.isfinite(quat.y) and
                   math.isfinite(quat.z) and math.isfinite(quat.w)):
                self.get_logger().warn('Invalid orientation data: NaN or infinite values detected')
                return False

            # Check quaternion normalization (should be close to 1.0)
            quat_norm = math.sqrt(quat.x**2 + quat.y**2 + quat.z**2 + quat.w**2)
            if abs(quat_norm - 1.0) > 0.1:  # Allow some tolerance
                self.get_logger().warn(f'Quaternion not normalized: norm={quat_norm:.3f}')
                return False

            # Check velocity data
            vel = msg.twist.twist.linear
            ang_vel = msg.twist.twist.angular
            max_velocity = self.max_velocity_bound  # Use configurable parameter
            max_angular_velocity = self.max_angular_velocity_bound  # Use configurable parameter

            if not (math.isfinite(vel.x) and math.isfinite(vel.y) and math.isfinite(vel.z)):
                self.get_logger().warn('Invalid linear velocity data')
                return False

            if not (math.isfinite(ang_vel.x) and math.isfinite(ang_vel.y) and math.isfinite(ang_vel.z)):
                self.get_logger().warn('Invalid angular velocity data')
                return False

            if (abs(vel.x) > max_velocity or abs(vel.y) > max_velocity or
                abs(ang_vel.z) > max_angular_velocity):
                self.get_logger().warn(f'Velocity out of bounds: linear={vel.x:.2f}, angular={ang_vel.z:.2f}')
                return False

            # Check for position jumps (if we have previous data)
            if hasattr(self, 'current_pose') and self.current_pose is not None:
                prev_pose = self.current_pose.position
                dx = pose.x - prev_pose.x
                dy = pose.y - prev_pose.y
                distance_jump = math.sqrt(dx**2 + dy**2)

                # Maximum allowed position jump per update (configurable parameter)
                max_jump = self.max_position_jump

                if distance_jump > max_jump:
                    self.get_logger().warn(f'Large position jump detected: {distance_jump:.2f}m (max: {max_jump:.2f}m)')
                    return False

            return True

        except Exception as e:
            self.get_logger().error(f'Error validating odometry data: {e}')
            return False

    def _resume_navigation_callback(self, request, response):
        """
        手动恢复导航服务回调
        ==================

        处理外部手动恢复导航的服务请求。
        允许操作员在里程计问题解决后手动恢复导航。

        参数:
            request: 服务请求（Trigger类型，无特定内容）
            response: 服务响应，包含成功状态和消息
        """
        # 避免未使用参数警告
        _ = request

        try:
            if self.navigation_paused_due_to_odom_loss:
                self.get_logger().info('通过服务调用手动恢复导航')
                self.navigation_paused_due_to_odom_loss = False
                self.was_navigating_before_odom_loss = False

                # 重置PID控制器以实现干净的重启
                self._reset_pid_controllers()

                # 可选：重新计算最近的路径点
                if self.recalculate_waypoint_on_recovery:
                    self._recalculate_target_waypoint()

                response.success = True
                response.message = "导航恢复成功"
                self.get_logger().info('导航已手动恢复')
            else:
                response.success = False
                response.message = "导航未因里程计丢失而暂停"
                self.get_logger().warn('Resume navigation called but navigation is not paused')

        except Exception as e:
            response.success = False
            response.message = f"Failed to resume navigation: {str(e)}"
            self.get_logger().error(f'Error resuming navigation: {e}')

        return response

    def _check_position_jump(self, new_pose) -> bool:
        """Check for large position jumps that might indicate odometry errors"""
        if self.previous_pose is None:
            # First pose reading, accept it
            self.previous_pose = new_pose
            self.position_stabilization_count = 1
            return True

        # Calculate position change
        dx = new_pose.position.x - self.previous_pose.position.x
        dy = new_pose.position.y - self.previous_pose.position.y
        position_change = math.sqrt(dx**2 + dy**2)

        # Check if position change is reasonable (use configured threshold)
        max_reasonable_change = self.position_jump_threshold

        if position_change > max_reasonable_change:
            # Large position jump detected
            self.large_position_jump_detected = True
            self.position_stabilization_count = 0
            self.get_logger().warn(
                f'Large position jump detected: {position_change:.2f}m '
                f'(threshold: {max_reasonable_change:.2f}m)'
            )
            return False
        else:
            # Position change is reasonable
            if self.large_position_jump_detected:
                # We're recovering from a position jump
                self.position_stabilization_count += 1

                if self.position_stabilization_count >= self.required_stable_readings:
                    # Position has stabilized
                    self.large_position_jump_detected = False
                    self.position_stabilization_count = 0
                    self.get_logger().info('Position stabilized after jump, resuming normal operation')
                    # Reset PID controllers to prevent using accumulated errors
                    self._reset_pid_controllers()
                else:
                    self.get_logger().info(
                        f'Position stabilizing: {self.position_stabilization_count}/{self.required_stable_readings}'
                    )

            self.previous_pose = new_pose
            return not self.large_position_jump_detected


def main(args=None):
    """Main function to run the navigation node"""
    rclpy.init(args=args)
    
    try:
        navigator = DifferentialTrackedNavigator()
        rclpy.spin(navigator)
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f'Error running navigator: {e}')
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()