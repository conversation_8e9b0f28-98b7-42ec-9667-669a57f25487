/**
 * @file differential_tracked_navigator.hpp
 * @brief ROS2差分履带车导航控制器C++头文件
 */

#ifndef DIFFERENTIAL_TRACKED_NAVIGATOR_HPP
#define DIFFERENTIAL_TRACKED_NAVIGATOR_HPP

#include <rclcpp/rclcpp.hpp>
#include <geometry_msgs/msg/twist_stamped.hpp>
#include <geometry_msgs/msg/point_stamped.hpp>
#include <nav_msgs/msg/odometry.hpp>
#include <nav_msgs/msg/occupancy_grid.hpp>
#include <nav_msgs/msg/path.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <std_msgs/msg/empty.hpp>
#include <std_msgs/msg/int8.hpp>
#include <std_srvs/srv/trigger.hpp>

#include <memory>
#include <vector>

/**
 * @class PIDController
 * @brief PID控制器类声明
 */
class PIDController;

/**
 * @struct VehicleParameters
 * @brief 车辆参数结构声明
 */
struct VehicleParameters;

/**
 * @class DifferentialTrackedNavigator
 * @brief 差分履带车导航控制器类声明
 */
class DifferentialTrackedNavigator : public rclcpp::Node
{
public:
    /**
     * @brief 构造函数
     */
    DifferentialTrackedNavigator();

    /**
     * @brief 析构函数
     */
    ~DifferentialTrackedNavigator() = default;

private:
    // 私有方法声明
    void declareParameters();
    void loadParameters();
    void setupPublishers();
    void setupSubscribers();
    void setupServices();
    
    // 回调函数声明
    void pathCallback(const nav_msgs::msg::Path::SharedPtr msg);
    void stopNavigationCallback(const std_msgs::msg::Int8::SharedPtr msg);
    void stateEstimationCallback(const nav_msgs::msg::Odometry::SharedPtr msg);
    void lidarCallback(const sensor_msgs::msg::PointCloud2::SharedPtr msg);
    void mapCallback(const nav_msgs::msg::OccupancyGrid::SharedPtr msg);
    
    // 控制相关方法声明
    void controlLoop();
    void publishCurrentWaypoint();
    bool reachedCurrentTarget();
    geometry_msgs::msg::TwistStamped calculateControlCommand();
    void stopVehicle();
    void resetPidControllers();
    void recalculateTargetWaypoint();
    
    // 工具方法声明
    double normalizeAngle(double angle);
    bool checkOdometryHealth();
    bool validateOdometryData(const nav_msgs::msg::Odometry::SharedPtr msg);
    bool checkPositionJump(const geometry_msgs::msg::Pose& new_pose);
    
    // 服务回调声明
    void resumeNavigationCallback(
        const std::shared_ptr<std_srvs::srv::Trigger::Request> request,
        std::shared_ptr<std_srvs::srv::Trigger::Response> response);

    // 成员变量声明省略（在源文件中定义）
};

#endif // DIFFERENTIAL_TRACKED_NAVIGATOR_HPP