#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试脚本：验证修改后的path_publisher_v2.py功能
"""

import rospy
from nav_msgs.msg import Path
import sys
import os

class PathSubscriber:
    """
    路径订阅者，用于测试path_publisher_v2.py发布的路径消息
    """
    def __init__(self):
        rospy.init_node('path_subscriber_test', anonymous=True)
        
        # 订阅/local_path话题
        self.path_sub = rospy.Subscriber('/local_path', Path, self.path_callback)
        
        rospy.loginfo("路径订阅者已启动，等待接收/local_path消息...")
        
    def path_callback(self, msg):
        """
        路径消息回调函数
        """
        rospy.loginfo(f"收到路径消息:")
        rospy.loginfo(f"  - 消息头: frame_id={msg.header.frame_id}")
        rospy.loginfo(f"  - 路径点数量: {len(msg.poses)}")
        
        # 打印前几个路径点的详细信息
        for i, pose in enumerate(msg.poses[:5]):  # 只打印前5个点
            rospy.loginfo(f"  - 路径点 {i+1}: x={pose.pose.position.x:.3f}, "
                         f"y={pose.pose.position.y:.3f}, "
                         f"yaw={pose.pose.orientation.z:.3f}")
        
        if len(msg.poses) > 5:
            rospy.loginfo(f"  - ... 还有 {len(msg.poses) - 5} 个路径点")
        
        rospy.loginfo("=" * 50)

def main():
    try:
        subscriber = PathSubscriber()
        rospy.spin()
    except rospy.ROSInterruptException:
        rospy.loginfo("测试被中断")

if __name__ == '__main__':
    main()
