#!/usr/bin/env python3
"""
临时测试脚本：验证横向偏移检测逻辑修改
=====================================

此脚本用于验证修改后的横向偏移检测逻辑是否正确工作。
"""

import math
from geometry_msgs.msg import Point

class MockPosition:
    def __init__(self, x, y, z=0.0):
        self.x = x
        self.y = y
        self.z = z

def test_lateral_deviation_logic():
    """测试横向偏移检测逻辑"""
    
    # 测试场景1：车辆与目标点距离小于阈值
    vehicle_pos = MockPosition(1.0, 1.0)
    target_pos = MockPosition(2.0, 1.5)
    
    dx = vehicle_pos.x - target_pos.x
    dy = vehicle_pos.y - target_pos.y
    distance = math.sqrt(dx**2 + dy**2)
    threshold = 2.0
    
    print(f"测试场景1 - 正常距离:")
    print(f"  车辆位置: ({vehicle_pos.x}, {vehicle_pos.y})")
    print(f"  目标位置: ({target_pos.x}, {target_pos.y})")
    print(f"  距离: {distance:.2f}m")
    print(f"  阈值: {threshold:.2f}m")
    print(f"  超过阈值: {'是' if distance > threshold else '否'}")
    print()
    
    # 测试场景2：车辆与目标点距离大于阈值
    vehicle_pos = MockPosition(1.0, 1.0)
    target_pos = MockPosition(4.0, 1.0)
    
    dx = vehicle_pos.x - target_pos.x
    dy = vehicle_pos.y - target_pos.y
    distance = math.sqrt(dx**2 + dy**2)
    
    print(f"测试场景2 - 偏移过大:")
    print(f"  车辆位置: ({vehicle_pos.x}, {vehicle_pos.y})")
    print(f"  目标位置: ({target_pos.x}, {target_pos.y})")
    print(f"  距离: {distance:.2f}m")
    print(f"  阈值: {threshold:.2f}m")
    print(f"  超过阈值: {'是' if distance > threshold else '否'}")
    print()
    
    # 测试场景3：模拟车辆沿路径行驶
    print("测试场景3 - 车辆沿路径行驶:")
    waypoints = [
        MockPosition(0.0, 0.0),
        MockPosition(5.0, 0.0),
        MockPosition(10.0, 5.0),
        MockPosition(15.0, 5.0)
    ]
    
    vehicle_positions = [
        MockPosition(1.0, 0.5),   # 接近第1个目标点
        MockPosition(4.0, 0.2),   # 接近第2个目标点
        MockPosition(8.0, 3.0),   # 向第3个目标点移动
        MockPosition(13.0, 5.5)   # 接近第4个目标点
    ]
    
    for i, (vehicle_pos, target_pos) in enumerate(zip(vehicle_positions, waypoints)):
        dx = vehicle_pos.x - target_pos.x
        dy = vehicle_pos.y - target_pos.y
        distance = math.sqrt(dx**2 + dy**2)
        
        print(f"  步骤 {i+1}:")
        print(f"    车辆位置: ({vehicle_pos.x}, {vehicle_pos.y})")
        print(f"    当前目标: ({target_pos.x}, {target_pos.y})")
        print(f"    距离: {distance:.2f}m")
        print(f"    超过阈值: {'是' if distance > threshold else '否'}")

if __name__ == "__main__":
    test_lateral_deviation_logic()