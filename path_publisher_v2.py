#!/usr/bin/env python
# -*- coding: utf-8 -*-

import rospy
import numpy as np
import argparse
import sys
import os
import time
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import Path
from tf.transformations import quaternion_from_euler

class PathPublisher:
    """
    从txt文件读取全局路径规划数据并发布完整路径到ROS话题 /local_path (nav_msgs/Path)
    """
    def __init__(self, file_path, rate=1.0, loop=False, start_index=0, repeat_count=None):
        """
        初始化路径发布器

        参数:
            file_path: txt文件路径，包含x,y,yaw数据
            rate: 发布频率 (Hz)
            loop: 是否循环发布路径
            start_index: 起始索引，从路径的哪个点开始发布
            repeat_count: 发布次数，None表示无限制
        """
        # 初始化ROS节点
        rospy.init_node('path_publisher', anonymous=True)

        # 创建发布者
        self.path_pub = rospy.Publisher('/local_path', Path, queue_size=10)

        # 设置发布频率
        self.rate = rospy.Rate(rate)

        # 读取路径数据
        self.path_data = self.read_path_file(file_path)

        if len(self.path_data) == 0:
            rospy.logerr("路径数据为空，请检查文件路径和格式")
            sys.exit(1)

        self.loop = loop
        self.total_points = len(self.path_data)
        self.repeat_count = repeat_count
        # start_index 参数在Path消息中不再需要，因为我们发布完整路径
        _ = start_index  # 避免未使用参数警告

        rospy.loginfo(f"成功读取路径数据，共 {self.total_points} 个点")
        rospy.loginfo(f"发布频率: {rate} Hz")
        rospy.loginfo(f"循环模式: {'开启' if loop else '关闭'}")
        rospy.loginfo(f"发布话题: /local_path (nav_msgs/Path)")
        rospy.loginfo(f"发布次数: {'无限制' if repeat_count is None else repeat_count}")

    def read_path_file(self, file_path):
        """
        读取包含x,y,yaw数据的txt文件

        参数:
            file_path: txt文件路径

        返回:
            path_data: 包含x,y,yaw的numpy数组
        """
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                rospy.logerr(f"文件不存在: {file_path}")
                return []

            # 读取文件数据
            path_data = np.loadtxt(file_path, delimiter=None)

            # 处理只有一行数据的情况
            if len(path_data.shape) == 1:
                # 如果是一维数组（只有一行），检查元素数量
                if path_data.shape[0] < 3:
                    rospy.logerr(f"文件格式错误: 需要至少3列数据 (x,y,yaw)，但只有 {path_data.shape[0]} 列")
                    return []
                # 将一维数组转换为二维数组（1行3列）
                path_data = path_data[:3].reshape(1, 3)
                rospy.loginfo("检测到单行数据，已正确处理")
            else:
                # 如果是二维数组（多行），检查列数
                if path_data.shape[1] < 3:
                    rospy.logerr(f"文件格式错误: 需要至少3列数据 (x,y,yaw)，但只有 {path_data.shape[1]} 列")
                    return []
                # 只取前三列 (x,y,yaw)
                path_data = path_data[:, :3]

            return path_data

        except Exception as e:
            rospy.logerr(f"读取文件时出错: {str(e)}")
            return []

    def create_path_msg(self):
        """
        创建Path消息，包含所有路径点

        返回:
            path_msg: Path消息
        """
        path_msg = Path()

        # 设置消息头
        path_msg.header.stamp = rospy.Time.now()
        path_msg.header.frame_id = "map"

        # 为每个路径点创建PoseStamped消息
        for x, y, yaw in self.path_data:
            pose_stamped = PoseStamped()

            # 设置消息头
            pose_stamped.header.stamp = rospy.Time.now()
            pose_stamped.header.frame_id = "map"

            # 设置位置
            pose_stamped.pose.position.x = x
            pose_stamped.pose.position.y = y
            pose_stamped.pose.position.z = 0.0

            # 设置方向（简化处理，直接使用yaw值）
            pose_stamped.pose.orientation.x = 0
            pose_stamped.pose.orientation.y = 0
            pose_stamped.pose.orientation.z = yaw
            pose_stamped.pose.orientation.w = 0

            # 添加到路径中
            path_msg.poses.append(pose_stamped)

        return path_msg

    def publish_path(self):
        """
        发布完整路径数据到ROS话题
        """
        try:
            # 创建完整的路径消息
            path_msg = self.create_path_msg()

            rospy.loginfo(f"开始发布完整路径，包含 {len(path_msg.poses)} 个路径点")

            published_count = 0

            while not rospy.is_shutdown():
                # 发布完整路径
                self.path_pub.publish(path_msg)
                published_count += 1

                rospy.loginfo(f"发布完整路径 (第 {published_count} 次)")

                # 检查是否达到指定的发布次数
                if self.repeat_count is not None and published_count >= self.repeat_count:
                    rospy.loginfo(f"已完成 {published_count} 次发布，任务结束")
                    break

                # 如果不是循环模式，只发布一次
                if not self.loop:
                    rospy.loginfo("路径发布完毕（非循环模式）")
                    break

                # 按照指定频率发布
                self.rate.sleep()

        except rospy.ROSInterruptException:
            rospy.loginfo("发布被中断")
        except Exception as e:
            rospy.logerr(f"发布过程中出错: {str(e)}")

def main():
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='从txt文件读取路径数据并发布完整路径到ROS话题 /local_path (nav_msgs/Path)')
    parser.add_argument('file_path', type=str, help='包含x,y,yaw数据的txt文件路径')
    parser.add_argument('--rate', type=float, default=1.0, help='发布频率 (Hz)，默认为1Hz')
    parser.add_argument('--loop', action='store_true', help='循环发布路径')
    parser.add_argument('--start', type=int, default=0, help='起始索引，从路径的哪个点开始发布')
    parser.add_argument('--count', type=int, default=None, help='发布次数，默认为无限制')

    # 解析命令行参数
    args = parser.parse_args()

    # 创建并启动路径发布器
    publisher = PathPublisher(
        file_path=args.file_path,
        rate=args.rate,
        loop=args.loop,
        start_index=args.start,
        repeat_count=args.count
    )
    publisher.publish_path()

if __name__ == '__main__':
    main()
