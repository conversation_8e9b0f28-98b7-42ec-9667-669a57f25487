# Path Publisher v2 使用说明

## 概述

`path_publisher_v2.py` 已经修改为发布完整路径到 `/local_path` 话题，消息类型为 `nav_msgs/Path`。

## 主要修改

1. **话题名称**: 从 `/web_goal_pose` 改为 `/local_path`
2. **消息类型**: 从 `geometry_msgs/PoseStamped` 改为 `nav_msgs/Path`
3. **发布方式**: 从逐个发布路径点改为一次性发布完整路径

## 使用方法

### 基本用法

```bash
# 发布一次完整路径
python path_publisher_v2.py test_path_data.txt

# 循环发布路径（每秒一次）
python path_publisher_v2.py test_path_data.txt --loop --rate 1.0

# 发布指定次数
python path_publisher_v2.py test_path_data.txt --count 5
```

### 参数说明

- `file_path`: 路径数据文件（必需）
- `--rate`: 发布频率（Hz），默认1.0
- `--loop`: 循环发布模式
- `--count`: 发布次数，默认无限制
- `--start`: 起始索引（已废弃，因为现在发布完整路径）

### 数据文件格式

路径数据文件应包含 x, y, yaw 三列数据：

```
# 注释行以#开头
0.0 0.0 0.0
2.0 0.0 1.57
2.0 2.0 3.14
0.0 2.0 -1.57
0.0 0.0 0.0
```

## 测试

### 1. 启动测试订阅者

```bash
python test_path_publisher.py
```

### 2. 在另一个终端启动发布者

```bash
python path_publisher_v2.py test_path_data.txt --loop
```

### 3. 使用rostopic查看消息

```bash
# 查看话题信息
rostopic info /local_path

# 查看消息内容
rostopic echo /local_path

# 查看发布频率
rostopic hz /local_path
```

## 消息结构

发布的 `nav_msgs/Path` 消息包含：

- `header`: 消息头，包含时间戳和坐标系
- `poses[]`: PoseStamped数组，每个元素包含：
  - `header`: 子消息头
  - `pose`: 位姿信息
    - `position`: x, y, z坐标
    - `orientation`: 四元数表示的方向（简化处理）

## 注意事项

1. 方向信息使用简化处理，直接将yaw值存储在orientation.z中
2. 所有路径点的坐标系设置为"map"
3. start_index参数已废弃，因为现在发布完整路径
4. 路径消息一次性包含所有路径点，适合路径规划和可视化
